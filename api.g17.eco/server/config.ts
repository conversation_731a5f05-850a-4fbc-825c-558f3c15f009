/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

const defaultClientEmail = '<EMAIL>';
const isProduction = process.env.NODE_ENV === 'production';
const isTest = process.env.NODE_ENV === 'test';
const appEnv = process.env.APP_ENV || 'development';

const oneDayInSeconds = 3600 * 24;
const sevenDaysInSeconds = oneDayInSeconds * 7;

const publicHostname = process.env.WWG_EMAIL_PUBLIC_HOSTNAME || 'https://www.staging.g17.eco';
const cloudEnv = process.env.CLOUD_PROVIDER === "azure" ? "azure" : "google";
const release = process.env.SENTRY_RELEASE
const releaseDist = process.env.SENTRY_RELEASE_DIST;

const traceSampleRate = process.env.SENTRY_TRACE_SAMPLE_RATE;
const profileSampleRate = process.env.SENTRY_PROFILE_SAMPLE_RATE;

export default {
  isProduction,
  appEnv: appEnv,
  cloudEnv: cloudEnv,
  release: {
    release: release || '-',
    build: releaseDist || '-',
  },
  containerEnv: process.env.KUBERNETES ? 'kubernetes' : 'native',
  localJobRunEnv: Boolean(process.env.LOCAL_JOB_RUN),
  regions: {
    singapore: {
      url: process.env.PLATFORM_SINGAPORE_URL || 'https://singapore.g17.eco/',
      label: "Singapore & Asia"
    },
    international: {
      url: process.env.PLATFORM_INTERNATIONAL_URL || 'https://monitoring.g17.eco/',
      label: "International (inc. Europe, UK)"
    }
  },
  appSubdomain: process.env.APP_SUBDOMAIN, // Mostly for localhost: falls back to parsing subdomain from url
  preferredPPTXAppendix: process.env.PREFERRED_PPTX_APPENDIX || '',
  port: process.env.PORT || 4003,
  hostname: process.env.SERVER_LISEN_HOSTNAME || "0.0.0.0",
  logs: {
    level:  process.env.LOGS_LEVEL ?? 'info',
    sentry: {
      dsn: process.env.SENTRY_DSN || 'https://<EMAIL>/5280054',
      enabled: Boolean(isProduction || process.env.SENTRY_ENABLED),
      environment: appEnv,
      release: release,
      dist: releaseDist,
      tracesSampleRate: parseFloat(traceSampleRate || '0.01'),
      profilesSampleRate: profileSampleRate ? parseFloat(profileSampleRate) : undefined,
      sendDefaultPii: process.env.SENTRY_SEND_DEFAULT_PII ? process.env.SENTRY_SEND_DEFAULT_PII === 'true' : true,
    },
    console: {
      // Enable in cloud to associate with running docker container
      enabled: (!isTest && !isProduction) || Boolean(process.env.WWG_LOGGER_CONSOLE_ENABLED)
    },
    googleCloud: {
      enabled: cloudEnv === "google" && Boolean(process.env.WWG_CLOUD_LOGGER_ENABLED),
    }
  },
  telemetry: {
    tracing: {
      enabled: Boolean(process.env.WWG_TRACING_ENABLED),
      debug: Boolean(process.env.WWG_TRACING_DEBUG),
    }
  },
  database: {
    mongodb: {
      database: process.env.WWG_DB_NAME || 'wwg',
      uri: process.env.WWG_DB_URI || 'mongodb://localhost:27017',
      debug: Boolean(process.env.WWG_DB_DEBUG),
      readPreference: process.env.WWG_DB_READ_PREFERENCE,
    }
  },
  g17ecoReleases: {
    projectId: 'internal-nightly-g17-eco', // default - but can be overwritten by incoming release request
    credentials: {
      'private_key': process.env.WWG_RELEASES_PRIVATE_KEY,
      'client_email': process.env.WWG_RELEASES_CLIENT_EMAIL,
    },
    storage: {
      bucket: 'g17eco-releases-nightly' // default - but can be overwritten by incoming release request
    },
    secret: process.env.API_SECRET ?? ''
  },
  googleCloud: {
    // Need to set manually on local, cloud server resolve it automatically
    projectId: process.env.WWG_CLOUD_PROJECT_ID,
    credentials: {
      'private_key': process.env.WWG_CLOUD_PRIVATE_KEY,
      'client_email': process.env.WWG_CLOUD_CLIENT_EMAIL  || defaultClientEmail,
    },
    storage: {
      baseUrl: 'https://storage.googleapis.com',
      bucket: process.env.WWG_CLOUD_BUCKET_ASSETS || 'wwg-dev-activeledger-cluster-assets',
    },
    cloudRun: {
      job: {
        name: process.env.CLOUD_RUN_JOB_NAME || 'background-bulk-survey-create',
        region: process.env.CLOUD_RUN_JOB_REGION || 'europe-west2',
      }
    }
  },
  azure: {
    storage: {
      baseUrl: process.env.AZURE_STORAGE_URL ?? "MissingURL",
      containerName: process.env.AZURE_STORAGE_CONTAINER_NAME ?? '-',
      accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME ?? '-',
      accountKey: process.env.AZURE_STORAGE_ACCOUNT_KEY ?? '-',
    },
  },
  kubernetes: {
    backgroundJob: {
      templateName: 'g17eco-job-template',
      namespace: 'g17eco-background-job',
    }
  },
  jwt: {
    disableSingleSession: process.env.AUTH_DISABLE_SINGLE_SESSION,
    secret: process.env.WWG_AUTH_JWT_SECRET || 'o17g32qo8erbf7oc4n7erbfos4eilr7nu',
    accessTokenExpire: oneDayInSeconds,
    refreshTokenExpire: sevenDaysInSeconds,
    emailAccessTokenExpire: oneDayInSeconds * 14,
  },
  subscription: {
    tokenExpiration: '1y'
  },
  payment: {
    stripe: {
      secret: process.env.STRIPE_SECRET_KEY || 'sk_test_51J5Xh2AnOGGCEUYPVTn86zZ7omDYsZTQxDZE7kgJAZFDih6OYuYW1ISC5g2PZqLUnkkQcD8QwPYxlsAIgOJN1s1q00dpWRcL7a',
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_51J5Xh2AnOGGCEUYPTjU110CdSR4rHchEb3NTseCpfdGqG9mGK3f5faJ3ZE9xtKylgsRft9gakZrdSSEpIsk5R2bI00Ktf1Ysez',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_4NgHFogxD0chiRHFNhJ8uX3OTQ1h0u4j',
    }
  },
  authentication: {
    oidc: {
      clientId: process.env.OKTA_CLIENT_ID || '0oa26olz8zJVYCM7T5d7',
      issuer: process.env.OKTA_ISSUER || 'https://dev-login.g17.eco/oauth2/aus2a1qzwisQ8fZQX5d7',
      assertClaims: {
        aud: `api://default`
      },
    },
    apiToken: process.env.OKTA_API_TOKEN ?? 'PLEASE_PROVIDE_VALID_TOKEN',
    orgUrl: process.env.OKTA_ORG_URL || 'https://dev-login.g17.eco',
    appGroup: process.env.OKTA_GROUP_ID || '00g2ans40aEafWEMX5d7',
    appStaffGroup: process.env.OKTA_STAFF_GROUP_IDS || '00g274j672OV9BbQG5d7,00g27g4anpz4zuvE55d7',
    idpMapping: process.env.OKTA_IDP_MAPPING || 'wwg:0oa27fuuku5yUjv175d7:World Wide Generation:wwg.svg,lvmh:0oa27wdymddULF3mG5d7:LVMH:lvmh_57.png',
  },
  integrations: {
    greenProject: {
      loginUrl: process.env.GREEN_PROJECT_LOGIN_URL || 'https://app.preprod.greenprojecttech.com',
      email: process.env.GREEN_PROJECT_EMAIL || '<EMAIL>',
      oktaGroupId: process.env.GREEN_PROJECT_OKTA_GROUP_ID || '00ghn0nfcbek8hpoj5d7',
      api: {
        tokenUrl: process.env.GREEN_PROJECT_TOKEN_BASE_URL || 'https://auth.app.preprod.greenprojecttech.com',
        baseUrl: process.env.GREEN_PROJECT_API_BASE_URL || 'https://connect.preprod.greenprojecttech.com/api/1.0.5',
        credentials: {
          // These change often, as GPT reset their credentials after syncing data from Prod
          clientId: process.env.GREEN_PROJECT_CLIENT_ID || '',
          clientSecret: process.env.GREEN_PROJECT_CLIENT_SECRET || '',
          grantType: 'client_credentials',
        },
        // Should be strict to ensure multiple API calls do not cause issues
        timeoutMs: Number(process.env.GREEN_PROJECT_TIMEOUT_MS || 3000),
      },
      // To allow local machine to use mock data
      useMockData: Boolean(process.env.GREEN_PROJECT_USE_MOCK_DATA),
    },
    greenly: {
      enabled: Boolean(process.env.GREENLY_ENABLED),
      loginUrl: process.env.GREENLY_LOGIN_URL || 'https://carbon.greenly.earth',
      email: process.env.GREENLY_EMAIL || '<EMAIL>',
      oktaGroupId: process.env.GREENLY_OKTA_GROUP_ID || '00go7jupl3qy5Vvve5d7',
      validityStatus: process.env.GREENLY_VALIDITY_STATUS ?? 'TEST',
      api: {
        tokenUrl: process.env.GREENLY_TOKEN_BASE_URL || 'https://greenly-production.eu.auth0.com',
        baseUrl: process.env.GREENLY_API_BASE_URL || 'https://api.greenly.tech/api/v2',
        credentials: {
          clientId: process.env.GREENLY_CLIENT_ID || '',
          clientSecret: process.env.GREENLY_CLIENT_SECRET || '',
          grantType: 'client_credentials',
          audience: 'https://corporate.greenly.earth/api/v2'
        },
        // Should be strict to ensure multiple API calls do not cause issues
        timeoutMs: Number(process.env.GREENLY_TIMEOUT_MS || 3000),
      },
      // To allow local machine to use mock data
      useMockData: Boolean(process.env.GREENLY_USE_MOCK_DATA),
    }
  },
  legal: {
    termsAndConditions: process.env.LEGAL_TERMS_AND_CONDITIONS_URL || `${publicHostname}/legal-terms`,
    privacyPolicy: process.env.LEGAL_PRIVACY_POLICY_URL || `${publicHostname}/legal-privacy-policy`,
  },
  email: {
    supportEmail: '<EMAIL>',
    platformEmail: '<EMAIL>',
    infoEmail: '<EMAIL>',
    leadsEmail: process.env.WWG_LEADS_EMAIL || '<EMAIL>',
    publicHostname: publicHostname,
    allowOrigin: process.env.WWG_ALLOW_ORIGIN || `${publicHostname},https://sg.staging.g17.eco,https://singapore.staging.g17.eco,https://singapore.demo.g17.eco,http://localhost:4001`,
    marketingSiteHostname: 'https://www.worldwidegeneration.co',
    adminHostname: process.env.WWG_EMAIL_ADMIN_HOSTNAME || 'https://admin.staging.g17.eco',
    defaultSender: process.env.EMAIL_DEFAULT_SENDER_EMAIL || '<EMAIL>',
    defaultSenderName: process.env.EMAIL_DEFAULT_SENDER_NAME || 'G17Eco',
    mailChimpKey: process.env.MAILCHIMP_KEY || '*************************************',
    mailChimpServer: process.env.mailChimpServer || process.env.MAILCHIMP_SERVER || 'us18',
    mailChimpAudienceId: process.env.MAILCHIMP_AUDIENCEID || '35e5d6c377'
  },
  assets: {
    cdn: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com',
    logoSrcRoot: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/logos',
    defaultLogo: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/images/wwg-logo-180h.png'
  },
  queue: {
    client: {
      type: 'sqs',
      options: {
        region: 'eu-west-1',
        accessKeyId: process.env.SES_ACCESS_KEY,
        secretAccessKey: process.env.SES_SECRET_ACCESS_KEY,
      },
    },
    ledger: {
      queueName: process.env.SQS_LEDGER_NAME || 'staging-qldb-lambda-messages',
    },
    emailFeedback: {
      queueName: process.env.SQS_EMAIL_FEEDBACK_NAME || 'nightly-ses-feedback',
    },
    webhook:{
      secret: process.env.SQS_WEBHOOK_SECRET || 'jJU83N9GDhPTPpnpqISC'
    }
  },
  ledger: {
    type: 'qldb',
    options: {
      ledgerName: process.env.QLDB_LEDGER_NAME || 'g17eco-staging',
      region: 'eu-west-1',
      accessKeyId: process.env.QLDB_ACCESS_KEY,
      secretAccessKey: process.env.QLDB_SECRET_ACCESS_KEY,
    },
  },
  websocket: {
    url: process.env.WEBSOCKET_URL,
  },
  ledgerMicroservice: {
    host: process.env.LEDGER_SERVICE_HOST || '',
    port: process.env.LEDGER_SERVICE_PORT || 4010
  },
  ethereum: {
    options: {
      contractAddress: process.env.ETHEREUM_CONTRACT_ADDRESS,
      domain: process.env.ETHEREUM_DOMAIN,
    },
  },
  dataIntegrationMicroservice: {
    host: process.env.DATA_INTEGRATION_SERVICE_HOST || 'localhost',
    port: process.env.DATA_INTEGRATION_SERVICE_PORT ? parseInt(process.env.DATA_INTEGRATION_SERVICE_PORT) : 3010
  },
  cache: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || '6379',
    ttl: Number(process.env.REDIS_TTL ?? 7200),
    // Anything before this date is not valid
    bundleZipDate: new Date('2023-01-31T18:00:00.000Z'),
  },
  crm: {
    salesforce: {
      enabled: Boolean(process.env.SF_ENABLED),
      user: process.env.SF_USER,
      password: process.env.SF_USER_PASSWORD + '' + process.env.SF_USER_SECURITY_TOKEN,
      leadUrl: 'https://webto.salesforce.com/servlet/servlet.WebToLead?encoding=UTF-8',
      leadOid: '00D3z000001BYGG',
      messageField: '00N3z000009qegN',
      interestedInField: '00N3z00000AarTo',
      organizationId: '00D3z000001BYGGEA4',
      whereDidYouHearField: '00NRz000000JXy1',
      siteSourceField: '00NRz000000Kz7N',
    },
  },
  referrals: {
    abs: process.env.ABS_METRIC_GROUP_IDS || '',
  },
  notifications: {
    magicBell: {
      apiKey: process.env.MAGIC_BELL_API_KEY || '784344a7c9af567c9548a59ed2b35156efbb56d1',
      apiSecret: process.env.MAGIC_BELL_SECRET_KEY || "34ed8460fda67e36e1346817546f84fdcd360ac6",
    },
    slack: {
      botToken: process.env.SLACK_BOT_TOKEN,
      channels: {
        signup: process.env.SLACK_SIGNUP_CHANNEL_ID || 'C05LVEC7CUC',
      },
    },
    dailyUser: {
      cronTime: process.env.NOTIFICATION_DAILY_USER_CRON_TIME || '00:00:00',
    }
  },
  analytics: {
    anonymousId: `${appEnv}.g17.eco`,
    segment: {
      writeKey: process.env.SEGMENT_WRITE_KEY || 'yvnBZiCq12my1Uyl7DSTT602CB3cvhtL',
    }
  },
  service: {
    ipStack: {
      accessKey: process.env.IP_STACK_ACCESS_KEY,
      cache: {
        TTL: 1000 * 60 * 60 * 24, // 1 day
      }
    }
  },
  sftp: {
    sgx: {
      host: process.env.SGX_SFTP_HOST,
      port: process.env.SGX_SFTP_PORT,
      userId: process.env.SGX_SFTP_USERID,
      privateKey: process.env.SGX_SFTP_PK,
      destinationPath: process.env.SGX_SFTP_DESTINATION_PATH || '/outbox/',
    }
  },
  sgx: {
    issuer: {
      bucketName: process.env.SGX_ISSUER_BUCKET || 'g17eco-integrations',
      filePath: process.env.SGX_ISSUER_FILEPATH || 'sgx/MFF_ISR_FILE.xml',
      sftp: {
        host: process.env.SGX_ISSUER_SFTP_HOST,
        port: process.env.SGX_ISSUER_SFTP_PORT,
        userId: process.env.SGX_ISSUER_SFTP_USERID,
        privateKey: process.env.SGX_ISSUER_SFTP_PK,
      }
    },
    export: {
      bucketName: process.env.SGX_ISSUER_BUCKET || 'g17eco-integrations',
      folder: 'sgx/outbox'
    }
  },
  ai: {
    chatGPT: {
      apiKey: process.env.OPENAI_API_KEY ?? 'sk-developer-i971xU94iWi4pr2J20EpT3BlbkFJGJoV7BtQoRXYhQM6vZNC',
    },
    claude: {
      apiKey:
        process.env.CLAUDE_API_KEY ??
        '************************************************************************************************************',
    },
    gemini: {
      apiKey: process.env.GEMINI_API_KEY ?? '',
    },
  },
  deployment: {
    authorizationToken: 'jK#Y@s*ZUmLjk88xPPNZv2Qjo7HR7TUgWB3'
  }
} as const;

