/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import type { Tracer } from '@opentelemetry/api';
import config from '../config';
import * as Sentry from "@sentry/node";
import HttpError from "../error/HttpError";
import BadRequestError from "../error/BadRequestError";
import type { SamplingContext } from '@sentry/core';


// Must set up Sentry anyway
const {
  dsn,
  environment,
  enabled,
  tracesSampleRate,
  profilesSampleRate,
  release,
  dist,
  sendDefaultPii,
} = config.logs.sentry
const tracing = config.telemetry.tracing;

const shouldSkipSampler = (samplingContext: SamplingContext) => {
  // Check for target property, this is defined by Terraform healthcheck
  const attribute = samplingContext.attributes?.['http.target']

  if (typeof attribute !== 'string') {
    return false; // Cannot determine if should skip
  }

  // Routes that should not be traced
  if (attribute === '/' || attribute === '/api' || attribute.startsWith('/api?')) {
    return true;
  }

  // Disable health check traces
// GCP two health checks Managed Instance Group and Load Balancer
  return ['mig-healthcheck', 'lb-healthcheck'].some((q) => attribute.includes(q));
}

Sentry.init({
  enabled,
  dsn,
  environment,
  release,
  dist,
  skipOpenTelemetrySetup: !tracing.enabled,
  /**
   * https://docs.sentry.io/platforms/javascript/guides/express/configuration/sampling/#precedence
   *
   * Sampling precedence:
   *
   * 1. If a sampling decision is passed to startTransaction, that decision will be used, overriding everything else.
   *
   * 2. If tracesSampler is defined, its decision will be used.
   *  It can choose to keep or ignore any parent sampling decision, use the sampling context data
   *  to make its own decision, or choose a sample rate for the transaction.
   *  We advise against overriding the parent sampling decision because it will break distributed traces)
   *
   * 3. If tracesSampler is not defined, but there's a parent sampling decision, the parent sampling
   * decision will be used.
   * 4. If tracesSampler is not defined and there's no parent sampling decision, tracesSampleRate will be used.
   */
  tracesSampleRate: tracing.enabled ? tracesSampleRate : undefined,
  tracesSampler: tracing.enabled ? (samplingContext) => {
    // always inherit decision from parent if it exists - default logic without this function
    if (samplingContext.parentSampled !== undefined) {
      return samplingContext.parentSampled
    }

    if (shouldSkipSampler(samplingContext)) {
      return false;
    }
    return tracesSampleRate;
  } : undefined,
  integrations: profilesSampleRate ? [] : undefined,
  profilesSampleRate: profilesSampleRate ?? undefined,
  sendDefaultPii,
});


// These errors should not be logged.
const skipErrors = [
  BadRequestError,
];

export const shouldHandleError = (error: Error): boolean => {
  if (error instanceof HttpError && error.status === 401) {
    return false;
  }

  return !skipErrors.some(ErrorClass => error instanceof ErrorClass);
};
Sentry.expressErrorHandler({ shouldHandleError });

export const setupErrorMiddleware = Sentry.setupExpressErrorHandler;
export const SentryClient = Sentry;

export const initTracing = () => {
  const client = Sentry.getClient<Sentry.NodeClient>();
  const options = client?.getOptions();

  const logOps = options ? {
    enabled: options.enabled,
    tracesSampleRate: options.tracesSampleRate,
    profilesSampleRate: options.profilesSampleRate,
    environment: options.environment,
    release: options.release,
    dist: options.dist,
  } : undefined;

  if (logOps?.enabled) {
    console.info('Tracing enabled', logOps);
  }
}

export const getTracer = (name: string) => {
  return Sentry.getClient<Sentry.NodeClient>()?.tracer as Tracer;
}

