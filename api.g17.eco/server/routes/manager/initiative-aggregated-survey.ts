import express from 'express';
import Initiative from '../../models/initiative';
import UserError from '../../error/UserError';
import { ObjectId } from 'bson';
import { AuthRouter } from '../../http/AuthRouter';
import { getSurveyAggregator } from '../../service/survey/SurveyAggregator';
import { UtrvType } from '../../service/utr/constants';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { getAuditLogger } from '../../service/audit/AuditLogger';
import { SurveyAudit } from '../../service/audit/events/survey';
import { isFalsyOrEmpty } from '../../util/array';
import { mustValidate } from '../../util/validation';
import { getAutoAggregatedSurveyWorkflow } from '../../service/survey/workflow/AutoAggregatedSurveyWorkflow';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { SurveyType } from '../../util/survey';
import { createAggregateSurveyDto, updateAggregateSurveyDto } from '../validation-schemas/aggregate-survey';
import { DefaultBlueprintCode } from '../../survey/blueprints';
import { getUpdateAllAggregatedSurveyWorkflow } from '../../service/survey/workflow/UpdateAllAggregatedSurveyWorkflow';
import { InitiativeRepository } from '../../repository/InitiativeRepository';

const router = express.Router() as AuthRouter;
router.use(ContextMiddleware);

const surveyAggregator = getSurveyAggregator();
const auditLogger = getAuditLogger();

router.route('/aggregate').post((req, res, next) => {
  Initiative.findById(res.locals.initiativeId)
    .orFail()
    .exec()
    .then(async (initiative) => {

      const {
        scope,
        utrvType = UtrvType.Actual,
        sourceName = DefaultBlueprintCode,
        surveyIds,
        effectiveDate,
        name,
        period,
        filters,
      } = mustValidate(req.body, createAggregateSurveyDto);

      if (isFalsyOrEmpty(surveyIds)) {
        throw new UserError('You must select at least one survey', {
          debugMessage: 'Trying to create aggregated survey without source surveyIds',
          surveyIds,
        });
      }

      const survey = await surveyAggregator.createAggregatedSurveyWithValidation({
        name,
        company: initiative,
        type: SurveyType.Aggregation,
        effectiveDate,
        sourceName,
        surveyIdsToAggregate: surveyIds,
        utrvType,
        user: req.user,
        scope,
        period,
        filters,
      });

      res.FromModel(survey);
    })
    .catch(next);
});

router.route('/:surveyId/aggregate').patch((req, res, next) => {
  Initiative.findById(res.locals.initiativeId)
    .orFail()
    .exec()
    .then(async (initiative) => {
      const survey = await SurveyRepository.mustFindById(req.params.surveyId);
      const r = await surveyAggregator.updateAggregatedSurvey(survey, initiative, req.user);
      await auditLogger.fromContext({
        initiativeId: initiative._id,
        auditEvent: SurveyAudit.aggregateUpdate,
        targets: [auditLogger.initiativeTarget(initiative), auditLogger.surveyTarget(survey)],
      });

      res.FromModel(r);
    })
    .catch(next);
});

router.route('/:surveyId/update-aggregate-survey-config').patch(async (req, res, next) => {
  try {
    const { scope, surveyIds, name, period, filters } = mustValidate(req.body, updateAggregateSurveyDto);
    const initiative = await InitiativeRepository.mustFindById(res.locals.initiativeId);

    const result = await surveyAggregator.updateAggregatedSurveyConfig({
      data: {
        scope,
        name,
        period,
        surveyId: new ObjectId(req.params.surveyId),
        aggregatedSurveyIds: surveyIds.map((id) => new ObjectId(id)),
        filters,
      },
      initiative,
      user: req.user,
    });

    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router
  .route('/auto-aggregate')
  .get(async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(res.locals.initiativeId as string);
      const autoAggregatedSurvey = await SurveyRepository.findOne({ initiativeId, type: SurveyType.AutoAggregation });
      if (autoAggregatedSurvey) {
        return res.FromModel({ surveyId: autoAggregatedSurvey._id });
      }
      const surveyIds = await surveyAggregator.getAutoAggregatedSurveyIds(initiativeId);

      return res.FromModel({ canCreateAggregatedSurvey: !isFalsyOrEmpty(surveyIds) });
    } catch (e) {
      next(e);
    }
  })
  .post(async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(res.locals.initiativeId as string);
      const autoAggregatedSurvey = await SurveyRepository.findOne({ initiativeId, type: SurveyType.AutoAggregation });
      if (autoAggregatedSurvey) {
        return res.FromModel({ surveyId: autoAggregatedSurvey._id });
      }
      const autoAggregatedSurveyWorkflow = getAutoAggregatedSurveyWorkflow();

      const existingJob = await autoAggregatedSurveyWorkflow.findProcessingJob(initiativeId);

      if (existingJob) {
        return res.FromModel({ jobId: existingJob._id });
      }

      const createdJob = await autoAggregatedSurveyWorkflow.create({
        initiativeId: new ObjectId(res.locals.initiativeId as string),
        user: req.user,
      });
      return res.FromModel(createdJob);
    } catch (e) {
      next(e);
    }
  })
  .patch(async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(res.locals.initiativeId as string);

      const survey = await SurveyRepository.findOne({ initiativeId, type: SurveyType.AutoAggregation });

      if (!survey) {
        throw new UserError(`Cannot find auto aggregated survey with initiativeId: ${initiativeId}`);
      }

      const autoAggregatedSurveyWorkflow = getAutoAggregatedSurveyWorkflow();
      const createdJob = await autoAggregatedSurveyWorkflow.createUpdateWorkflow({
        initiativeId,
        surveyId: survey._id,
        user: req.user,
      });
      return res.FromModel(createdJob);
    } catch (e) {
      next(e);
    }
  });

router.route('/auto-aggregate/status')
.get(async (req, res, next) => {
  try {
    const initiativeId = new ObjectId(res.locals.initiativeId as string);

    const autoAggregatedSurveyWorkflow = getAutoAggregatedSurveyWorkflow();

    const existingJob = await autoAggregatedSurveyWorkflow.findProcessingJob(initiativeId);

    return res.FromModel(existingJob ? { status: existingJob.status, jobId: existingJob._id } : { status: 'notFound' });
  } catch (e) {
    next(e);
  }
})

router.route('/update-all').post(async (req, res, next) => {
  try {
    const updateAllAggregatedSurveyWorkflow = getUpdateAllAggregatedSurveyWorkflow();
    const createdJob = await updateAllAggregatedSurveyWorkflow.create({
      initiativeId: new ObjectId(res.locals.initiativeId as string),
      user: req.user,
    });
    return res.FromModel(createdJob);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
