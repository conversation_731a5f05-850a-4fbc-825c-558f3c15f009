import express from 'express';
import { <PERSON>th<PERSON>out<PERSON> } from '../../http/AuthRouter';
import { getAdminNotificationService } from '../../service/notification/AdminNotificationService';
import { requireStaffScopes } from '../../middleware/staffRoleMiddlewares';
import { StaffScope } from '../../models/staffRole';
import { mustValidate } from '../../util/validation';
import {
  systemNotificationFiltersSchema,
  dailyNotificationFiltersSchema,
  scheduledNotificationFiltersSchema,
  scheduleFiltersSchema,
} from '../validation-schemas/notificationFilters';
import { NotificationCategoryList } from '../../service/notification/NotificationTypes';
import { ScheduledType } from '../../models/scheduledNotification';
import { snakeToWords } from '../../util/string';

const router = express.Router() as AuthRouter;
const adminNotificationService = getAdminNotificationService();

// Require AdminRead permission for all notification routes
router.use(requireStaffScopes([StaffScope.AdminRead]));

/**
 * GET /api/admin/notifications/system
 * Get system notifications with filters
 */
router.route('/system').get(async (req, res) => {
  const filters = mustValidate(req.query, systemNotificationFiltersSchema);
  const result = await adminNotificationService.getSystemNotifications(filters);
  res.FromModel(result);
});

/**
 * GET /api/admin/notifications/daily
 * Get user daily notifications
 */
router.route('/daily').get(async (req, res) => {
  const filters = mustValidate(req.query, dailyNotificationFiltersSchema);
  const result = await adminNotificationService.getUserDailyNotifications(filters);
  res.FromModel(result);
});

/**
 * GET /api/admin/notifications/scheduled
 * Get scheduled notification campaigns
 */
router.route('/scheduled').get(async (req, res) => {
  const filters = mustValidate(req.query, scheduledNotificationFiltersSchema);
  const result = await adminNotificationService.getScheduledNotifications(filters);
  res.FromModel(result);
});

/**
 * GET /api/admin/notifications/schedules
 * Get notification schedules (email queue history)
 */
router.route('/schedules').get(async (req, res) => {
  const filters = mustValidate(req.query, scheduleFiltersSchema);
  const result = await adminNotificationService.getNotificationSchedules(filters);
  res.FromModel(result);
});


/**
 * GET /api/admin/notifications/types
 * Get available notification types and categories
 */
router.route('/types').get(async (_req, res) => {
  // Return static list of notification types and categories
  const types = {
    notificationTypes: Object.values(ScheduledType).map((value) => ({
      value,
      label: snakeToWords(value),
    })),
    categories: NotificationCategoryList,
    statuses: [
      { value: 'pending', label: 'Pending' },
      { value: 'completed', label: 'Completed' },
      { value: 'errored', label: 'Failed' },
      { value: 'skipped', label: 'Skipped' }
    ]
  };

  res.FromModel(types);
});

module.exports = router;
