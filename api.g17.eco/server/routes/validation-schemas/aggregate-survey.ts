import { z } from 'zod';
import { DataPeriods, UtrvType } from '../../service/utr/constants';
import { getObjectIdsSchema } from './common';
import { UtrvFilter } from '../../models/insightDashboard';
import { surveyScopeSchema } from './survey';

const filterObject = z
  .object({
    utrv: z.enum([UtrvFilter.AllAnswered, UtrvFilter.Verified] as const),
  });

export const createAggregateSurveyDto = z.object({
  surveyIds: getObjectIdsSchema(),
  utrvType: z.nativeEnum(UtrvType).optional(),
  sourceName: z.string().optional(),
  effectiveDate: z.string().optional(),
  name: z.string().optional(),
  period: z.nativeEnum(DataPeriods).optional(),
  scope: surveyScopeSchema.optional(),
  filters: filterObject,
});

export const updateAggregateSurveyDto = z.object({
  surveyIds: getObjectIdsSchema({ min: 1, isOptional: false }),
  name: z.string(),
  period: z.nativeEnum(DataPeriods),
  scope: surveyScopeSchema,
  filters: filterObject,
});
