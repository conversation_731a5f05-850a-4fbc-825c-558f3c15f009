/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, Model, model, Schema } from 'mongoose';
import { StakeholderGroup, StakeholderGroupSchema } from './stakeholderGroup';
import { ObjectId } from 'bson';
import { EscalationPolicy, EscalationPolicySchema } from './escalationPolicy';
import type {
  AssurancePortfolioModel,
  AssurancePortfolioPlain,
} from '../service/assurance/model/AssurancePortfolio';
import { InitiativePlain } from './initiative';
import type { UnitConfig } from '../types/units';
import { UnitConfigSchema } from '../service/units/unitTypes';
import type { Blueprint } from '../repository/BlueprintRepository';
import { BlueprintSchema } from './common/blueprintSchema';
import { DelegationScopeSchema } from './common/delegationScopeSchema';
import { Roles } from "./public/surveyType";
import { DataPeriods, type UtrvType } from '../types/constants';
import type { UniversalTrackerPlain } from './universalTracker';
import type { CompositeDataSurveyId, UniversalTrackerValuePlain } from './universalTrackerValue';
import type { QuestionGroup } from '../service/survey/SurveyCalculator';
import type { MetricGroupPlain } from './metricGroup';
import type { BlueprintContributions } from '../service/survey/BlueprintContribution';
import { ValueList } from './public/valueList';
import type { RootInitiativeData } from '../repository/InitiativeRepository';
import { GroupType } from '../survey/utrGroupConfigs';
import type { UserModel } from './user';
import type { KeysEnum } from "./public/projectionUtils";
import { ScheduledDate, ScheduledDateSchema } from './scheduledNotification';
import { EditorState } from '../types/editorState';
import { CustomScope } from "./customScopeSchema";
import { SurveyUserRoles } from '../types/roles';
import { DataScopeAccess } from './dataShare';
import { Permission } from '../types/permission';
import { type Scope, ScopeSchema } from './common/scope';
export { type Scope, ScopeSchema } from './common/scope';
import { UtrvFilter } from '../types/universalTrackerValue';
import { AssessmentType } from '../types/materiality-assessment';
import { UtrSortingConfig } from '../survey/utr-sorting/types';

/** @deprecated should import from util/survey.ts */
export enum SurveyType {
  Default = 'default',
  Aggregation = 'aggregation',
  Materiality = 'materiality',
  AutoAggregation = 'auto_aggregation'
}

/** @deprecated should import from util/survey.ts */
export const isAggregatedSurvey = (type: unknown): type is SurveyType.Aggregation | SurveyType.AutoAggregation =>
  [SurveyType.Aggregation, SurveyType.AutoAggregation].includes(type as SurveyType);

/** @deprecated should import from util/survey.ts */
export const isMaterialitySurvey = (survey: Pick<SurveyModelPlain, 'type'>) => {
  return survey.type === SurveyType.Materiality;
};

export const BASED_SURVEY_TYPES = [SurveyType.Default, SurveyType.Aggregation] as const;

export enum SourceItemType {
  Survey = 'survey',
}

export type AllowedAggregatedUtrvStatus = UtrvFilter.AllAnswered | UtrvFilter.Verified;
export interface AggregatedSurveyFilters {
  utrv: AllowedAggregatedUtrvStatus;
}

export const ReferencesSchema = new Schema({
  surveyId: { type: Schema.Types.ObjectId, required: true },
  blueprint: { type: Schema.Types.String, required: true },
}, { _id: false });

export const SourceItemSchema = new Schema({
  sourceId: { type: Schema.Types.ObjectId, required: true },
  type: { type: Schema.Types.String, enum: Object.values(SourceItemType), required: true },
}, { _id: false });

export const RolesSchema = new Schema({
  admin: { type: [Schema.Types.ObjectId], default: [], required: true },
  viewer: { type: [Schema.Types.ObjectId], default: [], required: true },
}, { _id: false });

export interface ScopeWheelPreferences {
  scopeCode: string;
  visible: boolean;
}

interface DisplayPreferences {
  scopeWheels?: ScopeWheelPreferences[];
}

const ScopeWheelPreferencesSchema = new Schema<ScopeWheelPreferences>({
  scopeCode: {
    type: Schema.Types.String,
    required: true,
    trim: true
  },
  visible: {
    type: Schema.Types.Boolean,
    default: true
  }
}, { _id: false });

const DisplayPreferencesSchema = new Schema<DisplayPreferences>({
  scopeWheels: [ScopeWheelPreferencesSchema],
}, { _id: false });

export enum SurveyPermissionType {
  Workgroup = 'workgroup',
}

export interface SurveyPermission extends Permission<SurveyPermissionType, SurveyUserRoles> {
  /**
   * DataScopeAccess.Full: Have access to all scopes
   * DataScopeAccess.Partial: Have access to a subset of scopes that are listed in 'scope' field.
   * DataScopeAccess.None: No access to any scopes
   * Allow user to switch between full/none and partial access without reselect scopes.
   */
  access: DataScopeAccess;
  scope?: Scope;
}

const SurveyPermissionSchema = new Schema<SurveyPermission>(
  {
    type: {
      type: Schema.Types.String,
      enum: Object.values(SurveyPermissionType),
      required: true,
    },
    modelId: { type: Schema.Types.ObjectId, required: true },
    roles: {
      type: [Schema.Types.String],
      enum: Object.values(SurveyUserRoles),
      required: true,
    },
    access: {
      type: Schema.Types.String,
      enum: Object.values(DataScopeAccess),
      required: true,
    },
    scope: {
      type: ScopeSchema,
      required: false,
    },
  },
  { _id: false }
);

export const defaultSurveyFilters: AggregatedSurveyFilters = {
  utrv: UtrvFilter.Verified,
};

const FilterSchema = new Schema<AggregatedSurveyFilters>(
  {
    utrv: {
      type: Schema.Types.String,
      default: UtrvFilter.Verified,
      enum: [UtrvFilter.AllAnswered, UtrvFilter.Verified],
    },
  },
  { _id: false }
);

interface SurveyMethods<T = ObjectId> {
  isStakeholder(userId: T): boolean;
}

export const SurveySchema = new Schema<SurveyModelPlain, Model<SurveyModelPlain>, SurveyMethods>({
  code: {
    type: Schema.Types.String,
    unique: true,
    required: true,
    validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) '],
  },
  deletedDate: Schema.Types.Date,
  completedDate: Schema.Types.Date,
  aggregatedDate: {
    type: Schema.Types.Date,
    default: function (this: SurveyModel | undefined) {
      return this?.type && isAggregatedSurvey(this.type as SurveyType) ? new Date() : undefined;
    },
    required: function (this: SurveyModel) {
      return isAggregatedSurvey(this.type as SurveyType);
    },
  },
  aggregatedVersion: {
    type: Schema.Types.Number,
    default: 0,
    required: false,
  },
  name: { type: Schema.Types.String, required: false },
  sourceName: { type: Schema.Types.String, required: true },
  period: {
    type: Schema.Types.String,
    enum: Object.values(DataPeriods),
    default: DataPeriods.Yearly,
  },
  effectiveDate: Schema.Types.Date,
  utrvType: {
    type: Schema.Types.String,
    enum: ['actual', 'baseline', 'target'],
  },
  references: [ReferencesSchema],
  sourceItems: [SourceItemSchema],
  unitConfig: UnitConfigSchema,
  scope: ScopeSchema,
  delegationScope: DelegationScopeSchema,
  blueprint: BlueprintSchema,
  displayPreferences: DisplayPreferencesSchema,
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  stakeholders: { type: StakeholderGroupSchema, required: false },
  escalationPolicy: { type: EscalationPolicySchema, required: false },
  evidenceRequired: { type: Boolean, default: false, required: true },
  noteRequired: { type: Boolean, default: false },
  verificationRequired: { type: Boolean, default: true, required: true },
  isPrivate: Schema.Types.Boolean,
  visibleUtrvs: [Schema.Types.ObjectId],
  visibleStakeholders: [Schema.Types.ObjectId],
  roles: RolesSchema,
  permissions: [SurveyPermissionSchema],
  compositeUtrvs: [Schema.Types.ObjectId],
  fragmentUtrvs: [Schema.Types.ObjectId],
  disabledUtrvs: [Schema.Types.ObjectId],
  subFragmentUtrvs: [Schema.Types.ObjectId],
  type: {
    type: Schema.Types.String,
    enum: Object.values(SurveyType),
    required: true,
    default: SurveyType.Default,
  },
  created: { type: Schema.Types.Date, default: Date.now },
  creatorId: { type: Schema.Types.ObjectId, required: false },
  ignoredDate: { type: Schema.Types.Date, required: false },
  noteInstructions: {
    type: Schema.Types.String,
    trim: true,
    maxLength: 2000
  },
  noteInstructionsEditorState: Schema.Types.Mixed,
  deadlineDate: {
    type: Schema.Types.Date,
    required: function (this: SurveyModel) {
      return this.scheduledDates && this.scheduledDates.length > 0;
    },
  },
  scheduledDates: [ScheduledDateSchema],
  filters: {
    type: FilterSchema,
    default: function (this: SurveyModel | undefined) {
      return this?.type && isAggregatedSurvey(this.type as SurveyType) ? { utrv: UtrvFilter.Verified } : undefined;
    },
    required: function (this: SurveyModel) {
      return isAggregatedSurvey(this.type as SurveyType);
    }
  },
  assessmentType: {
    type: Schema.Types.String,
    enum: Object.values(AssessmentType),
    required: function (this: SurveyModel) {
      return this.type === SurveyType.Materiality;
    },
    default: function (this: SurveyModel | undefined) {
      if (this?.type === SurveyType.Materiality && !this.assessmentType) {
        this.assessmentType = AssessmentType.FinancialMateriality;
      }
    },
  },
}, { collection: 'surveys', toJSON: { virtuals: true } });

export interface DelegationScopeUsersGroup extends StakeholderGroup {
  inScope?: boolean;
  isPartial?: boolean;
  children?: DelegationScopeChildGroup[];
}

export interface DelegationScopeChildGroup extends DelegationScopeUsersGroup {
  code: string;
}

export interface DelegationScopeUsers<T = ObjectId> {
  [key: string]: DelegationScopeUsersGroup
}

export interface DelegationScope<T = ObjectId> {
  sdg: DelegationScopeUsers<T>;
  materiality: DelegationScopeUsers<T>;
  standards: DelegationScopeUsers<T>;
  frameworks: DelegationScopeUsers<T>;
  custom: DelegationScopeUsers<T>;
}

interface SourceItem<T = ObjectId> {
  type: SourceItemType,
  sourceId: T;
}

export interface SurveyModelMinData<T = ObjectId> {
  type: SurveyType;
  initiativeId: T;
  period: DataPeriods;
  effectiveDate: Date;
  utrvType: UtrvType;
  evidenceRequired: boolean;
  noteRequired?: boolean;
  /** deprecated Plain text note instructions, replaced by noteInstructionsEditorState **/
  noteInstructions?: string;
  /** JSON structure representing rich text editor state **/
  noteInstructionsEditorState?: EditorState;
  sourceItems?: SourceItem<T>[];
  verificationRequired: boolean;
  isPrivate?: boolean;
  visibleStakeholders: T[];
  visibleUtrvs: T[];
  stakeholders: StakeholderGroup<T>;
  code: string;
  name: string | undefined;
  sourceName: string;
  unitConfig: UnitConfig;
  scope?: Scope<T>;
  delegationScope?: DelegationScope;
  roles?: Roles;
  permissions?: SurveyPermission[];
  permissionGroup?: string;
  deadlineDate?: Date;
  scheduledDates?: ScheduledDate[];
  creatorId?: T;
  completedDate?: Date;
  assessmentType?: AssessmentType;
}

export interface ScopeUpdate {
  _id?: string;
  type: GroupType;
  name: string;
  added: string[],
  removed: string[],
}

SurveySchema.index({ compositeUtrvs: 1 }, { unique: false });
SurveySchema.index({ fragmentUtrvs: 1 }, { unique: false });
SurveySchema.index({ visibleUtrvs: 1 });
SurveySchema.index({ initiativeId: 1 });
SurveySchema.index({ visibleStakeholders: 1 });
SurveySchema.index({ 'references.surveyId': 1 });
SurveySchema.index({ initiativeId: 1, effectiveDate: -1, sourceName: 1 });
SurveySchema.index({ created: -1, sourceName: 1 });
SurveySchema.index({ sourceName: 1, deletedDate: 1, created: -1 }, { name: 'staff_portal_list_query' });

SurveySchema.virtual('assurance', {
  ref: 'AssurancePortfolio', // The model to use
  localField: '_id', // Find people where `localField`
  foreignField: 'surveyId', // is equal to `foreignField`
  justOne: false,
});

SurveySchema.virtual('initiative', {
  ref: 'Initiative', // The model to use
  localField: 'initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

SurveySchema.virtual('creator', {
  ref: 'User',
  localField: 'creatorId',
  foreignField: '_id',
  justOne: true,
})

export interface UtrvSurveyAction<T = ObjectId> {
  _id: T;
  name?: string;
  surveyId: T;
  initiativeId: T;
  status: string;
  escalationPolicy?: EscalationPolicy<T>;
  stakeholders?: StakeholderGroup;
}

export interface Company {
  permissionGroup?: string;
}

export type SurveyActionMinimalUtrv = Pick<UniversalTrackerValuePlain,
  '_id'
  | 'universalTrackerId'
  | 'initiativeId'
  | 'valueData'
  | 'value'
  | 'status'
  | 'stakeholders'
  | 'assuranceStatus'
  | 'lastUpdated'
  | 'evidenceRequired'
  | 'noteRequired'
  | 'verificationRequired'
  | 'isPrivate'
  | 'effectiveDate'
  | 'notes'
  | 'sourceItems'
  > & CompositeDataSurveyId;

export type SourceItemExtended = Pick<SurveyModelPlain, '_id' | 'initiativeId' | 'effectiveDate' | 'type' | 'period'>;

export interface SurveyActionDataAggregation<T = ObjectId>
  extends Pick<
    SurveyModelPlain,
    | 'noteInstructionsEditorState'
    | 'aggregatedDate'
    | 'period'
    | 'type'
    | 'assessmentType'
    | 'permissions'
    | 'visibleUtrvs'
  > {
  _id: T;
  code: string;
  delegationScope?: DelegationScope;
  effectiveDate: Date;
  evidenceRequired: boolean;
  noteRequired?: boolean;
  noteInstructions?: string;
  initiativeId: T;
  name: string | undefined;
  roles?: Roles;
  scope?: Scope<T>;
  sourceName: string;
  stakeholders: StakeholderGroup<T>;
  unitConfig: UnitConfig;
  utrvType: UtrvType;
  verificationRequired: boolean;
  created: string;
  completedDate?: Date;
  blueprint?: Blueprint;
  ignoredDate?: string;

  // injections
  fragmentUniversalTracker?: UniversalTrackerPlain[]; // Gets removed before return
  fragmentUniversalTrackerValues?: SurveyActionMinimalUtrv[];
  // assurances?: AssurancePortfolioPlain[];
  list: ValueList[];
  initiatives: Omit<RootInitiativeData, 'appConfigCode' | 'parents' | 'parentId'>[];
  displayPreferences?: DisplayPreferences;
  aggregatedSurveys?: SourceItemExtended[];
  filters?: AggregatedSurveyFilters;
}

export interface SurveyActionData<T = ObjectId> extends Omit<SurveyActionDataAggregation<T>, 'fragmentUniversalTracker' | 'blueprint'> {

  // Added outside query
  questionGroups: QuestionGroup[];
  scopeConfig?: CustomScope[];
  config?: Pick<Blueprint, 'unitConfig' | 'customGroups'>;

  // NEW
  customMetricGroups: MetricGroupPlain[];
  contributions: BlueprintContributions;

  utrSortingConfigMap: Record<string, UtrSortingConfig>;
  scopeUpdates?: ScopeUpdate[];
}

export type SurveyGroupsData = Pick<SurveyActionData,
  'scope' |
  'customMetricGroups' |
  'questionGroups' |
  'contributions' |
  'initiativeId' |
  'fragmentUniversalTrackerValues' |
  'initiatives'
>

type Reference<T = ObjectId> = { blueprint: string, surveyId: T };

export interface SurveyModelPlain<T = ObjectId> extends SurveyModelMinData<T> {
  _id: T;
  references?: Reference<T>[],
  compositeUtrvs: T[];
  fragmentUtrvs: T[];
  subFragmentUtrvs: T[];
  disabledUtrvs: T[];
  escalationPolicy?: EscalationPolicy<T>;
  created: Date;
  assurance?: AssurancePortfolioPlain[],
  initiative?: InitiativePlain,
  deletedDate?: Date;
  completedDate?: Date;
  aggregatedDate?: Date;
  aggregatedVersion?: number;
  blueprint?: Blueprint;
  displayPreferences?: DisplayPreferences;
  ignoredDate?: Date;
  filters?: AggregatedSurveyFilters;
}

export interface SurveyModelMinWithUtrvsPlain extends SurveyModelMinData {
  fragmentUniversalTrackerValues?: SurveyActionMinimalUtrv[];
  created: Date;
}

export interface SurveyModelWithAssurance<T = ObjectId> extends SurveyModelPlain<T> {
  assurance: AssurancePortfolioModel[],
}

export interface SurveyPlainExtended extends SurveyModelWithAssurance {
  initiative: InitiativePlain
}

export type SurveyModel<T = ObjectId> = HydratedDocument<SurveyModelPlain<T>> & SurveyMethods<T>;

export interface SurveyModelPlainWithInitiative<T = ObjectId> extends SurveyModelPlain<T> {
  initiative: InitiativePlain
}

export interface SurveyWithInitiative<T = ObjectId> extends SurveyModelPlain<T> {
  initiative: InitiativePlain
}


export type SurveyInitiativeMinPlain = Pick<InitiativePlain,
  | '_id'
  | 'code'
  | 'name'
  | 'profile'
  | 'type'
  | 'industryText'
  | 'startDate'
  | 'endDate'
  | 'industry'
  | 'parentId'
  | 'dataShare'
>

export interface SurveyInitiative<T extends string | ObjectId = ObjectId> {
  _id: T;
  initiativeId: T;
  initiative: SurveyInitiativeMinPlain;
  effectiveDate: Date;
  name?: string;
  completedDate?: Date;
  aggregatedDate?: Date;
  aggregatedVersion?: number;
  period?: DataPeriods;
  type?: SurveyType;
  sourceName?: string;
  unitConfig?: UnitConfig;
}


export const surveyInitiativeProjection: KeysEnum<SurveyInitiative, 1> = {
  _id: 1,
  aggregatedDate: 1,
  aggregatedVersion: 1,
  completedDate: 1,
  effectiveDate: 1,
  initiative: 1,
  initiativeId: 1,
  name: 1,
  period: 1,
  sourceName: 1,
  type: 1,
  unitConfig: 1
}

export const surveyInitiativeMinProjection: KeysEnum<SurveyInitiativeMinPlain, 1> = {
  _id: 1,
  code: 1,
  dataShare: 1,
  endDate: 1,
  industry: 1,
  industryText: 1,
  name: 1,
  parentId: 1,
  profile: 1,
  startDate: 1,
  type: 1
}

type SurveyCopiedProps = Pick<SurveyModelMinData,
  | 'type'
  | 'name'
  | 'isPrivate'
  | 'effectiveDate'
  | 'verificationRequired'
  | 'evidenceRequired'
  | 'noteRequired'
  | 'noteInstructions'
  | 'noteInstructionsEditorState'
  | 'sourceName'
  | 'scope'
  | 'unitConfig'
  | 'initiativeId'
  | 'period'
  | 'utrvType'
>
export interface BulkSurveysData extends SurveyCopiedProps, Pick<SurveyModelMinData, 'deadlineDate' | 'scheduledDates'> {
  templateId: ObjectId;
  useInitiativeSettings?: boolean;
  reportingLevels: ObjectId[];
  lastUpdated: string | Date;
  surveyName?: string;
}

export interface BulkAggregatedSurveysData
  extends Pick<
    SurveyModelMinData,
    | 'type'
    | 'initiativeId'
    | 'period'
    | 'unitConfig'
    | 'utrvType'
    | 'sourceName'
    | 'noteInstructions'
    | 'noteInstructionsEditorState'
    | 'deadlineDate'
    | 'scheduledDates'
  > {
  templateId: ObjectId;
  surveyName: string;
  hasCurrentInitiatives: boolean;
  reportingLevels: ObjectId[];
  effectiveDate: string;
}

export interface BulkSurveysScopeData {
  templateId: ObjectId;
  surveyIds: ObjectId[];
  user: UserModel;
}

export type SurveyListItem = Pick<SurveyModelPlain,
  | '_id'
  | 'name'
  | 'scope'
  | 'completedDate'
  | 'effectiveDate'
  | 'type'
  | 'period'
>


SurveySchema.methods.isStakeholder = function (userId: ObjectId): boolean {
  return this.stakeholders && this.stakeholders.stakeholder
    .map(String)
    .includes(userId.toString());
};

const Survey = model('Survey', SurveySchema);

export default Survey;
