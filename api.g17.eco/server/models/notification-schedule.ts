/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { type Model, model, Schema, type Types, type HydratedDocument } from 'mongoose';
import type { NotificationPreferencesPlain } from '../service/notification/NotificationModels';
import type { TimePeriod } from '../util/date';
import type { ObjectId } from 'bson';
import type { InitiativePlain } from './initiative';

export interface NotificationSchedule {
  isSummary: boolean;
  period: TimePeriod;
}

export interface NotificationSchedulePlain<T = Types.ObjectId> {
  userId: T;
  initiativeId: T;
  notificationIds: T[];
  completedDate?: Date;
  created?: Date; // Added by timestamps
  updated?: Date; // Added by timestamps
}

export interface NotificationScheduleExtended extends NotificationSchedulePlain {
  _id: ObjectId;
  notificationPreferences?: NotificationPreferencesPlain;
  initiative: InitiativePlain;
}

export type NotificationScheduleModel = HydratedDocument<NotificationSchedulePlain>;

const NotificationScheduleSchema = new Schema<NotificationScheduleModel>({
  // Ownership
  userId: { type: Schema.Types.ObjectId, required: true },
  // Group by initiative
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  // Accumulate notifications
  notificationIds: { type: [Schema.Types.ObjectId], default: [] },
  completedDate: Schema.Types.Date,
}, {
  collection: 'notification-schedules',
  timestamps: { createdAt: 'created', updatedAt: 'updated' } // Add default timestamps
});

NotificationScheduleSchema.index({ userId: 1, initiativeId: 1, completedDate: 1 });

NotificationScheduleSchema.virtual('user', {
  ref: 'User', // The model to use
  localField: 'userId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

NotificationScheduleSchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
});

const NotificationSchedule: Model<NotificationScheduleModel> = model<NotificationScheduleModel>('NotificationSchedule', NotificationScheduleSchema);

export default NotificationSchedule;
