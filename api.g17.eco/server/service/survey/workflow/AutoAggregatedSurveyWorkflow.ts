/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import BackgroundJob, {
  BackgroundJob<PERSON>odel,
  Background<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  JobStatus,
  JobType,
  Task,
  TaskStatus,
  TaskType,
} from '../../../models/backgroundJob';
import User, { UserPlain } from '../../../models/user';
import { HydratedDocument } from 'mongoose';
import { BackgroundJobService, getBackgroundJobService } from '../../background-process/BackgroundJobService';
import { generatedUUID } from '../../../service/crypto/token';
import UserError from '../../../error/UserError';
import ContextError from '../../../error/ContextError';
import { getCurrentDateStr } from '../../../util/date';
import { createLogEntry } from '../../../service/jobs';
import { isFalsyOrEmpty } from '../../../util/array';
import { SurveyAggregator, getSurveyAggregator } from '../SurveyAggregator';
import Initiative from '../../../models/initiative';
import { SurveyType } from '../../../models/survey';
import { DefaultBlueprintCode } from '../../../survey/blueprints';
import { UtrvType } from '../../../service/utr/constants';
import { SurveyRepository } from '../../../repository/SurveyRepository';
import { AggregationMode } from '../../../service/utr/aggregation/utrTypeAggregator';
import {
  BackgroundBaseWorkflow,
  JobInfo,
  TaskResult,
} from '../../../service/background-process/BackgroundBaseWorkflow';
import {
  CustomAttributes,
  NotificationCategory,
  NotificationPage,
} from '../../../service/notification/NotificationTypes';
import { NotificationService, getNotificationService } from '../../../service/notification/NotificationService';
import { UrlMapper } from '../../../service/url/UrlMapper';
import { getRootInitiativeService, RootInitiativeService } from '../../../service/organization/RootInitiativeService';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';

interface WorkflowCreate {
  initiativeId: ObjectId;
  user: Pick<UserPlain, '_id'>;
}

interface WorkflowUpdate extends WorkflowCreate {
  surveyId: ObjectId;
}

export type CreateAutoAggregatedSurveyTask = Task<
  {
    initiativeId: ObjectId;
  },
  TaskType.AutoAggregatedSurvey
>;

export type UpdateAutoAggregatedSurveyTask = Task<
  {
    initiativeId: ObjectId;
    surveyId: ObjectId;
  },
  TaskType.UpdateAutoAggregatedSurvey
>;

export type AutoAggregatedSurveyJobPlain = BackgroundJobPlain<
  (CreateAutoAggregatedSurveyTask | UpdateAutoAggregatedSurveyTask)[]
> & {
  initiativeId: ObjectId;
  type: JobType.AutoAggregatedSurvey;
};
export type AutoAggregatedSurveyJob = HydratedDocument<AutoAggregatedSurveyJobPlain>;

enum AutoAggregatedSurveyTasks {
  Create = 'create auto-aggregated survey',
  Update = 'update auto-aggregated survey',
}

export class AutoAggregatedSurveyWorkflow extends BackgroundBaseWorkflow<AutoAggregatedSurveyJob> {

  protected jobType = JobType.AutoAggregatedSurvey;

  constructor(
    protected logger: LoggerInterface,
    private bgJobService: BackgroundJobService,
    private surveyAggregator: SurveyAggregator,
    private notificationService: NotificationService,
    private rootInitiativeService: RootInitiativeService
  ) {
    super();
  }

  public isAutoAggregateJob(job: BackgroundJobModel): job is AutoAggregatedSurveyJob {
    return job.type === this.jobType && job.initiativeId !== undefined;
  }

  public async create(workflow: WorkflowCreate): Promise<JobInfo> {
    const { initiativeId } = workflow;

    const taskId = generatedUUID();

    const createTask: CreateAutoAggregatedSurveyTask = {
      id: taskId,
      name: 'Create a task for the auto-aggregation survey',
      type: TaskType.AutoAggregatedSurvey,
      status: TaskStatus.Pending,
      data: {
        initiativeId,
      },
    };

    // Ensure we are not spamming the same job again and again.
    const exists = await BackgroundJob.exists({
      type: this.jobType,
      initiativeId,
      status: { $in: [JobStatus.Pending, JobStatus.Processing] },
    });

    if (exists) {
      throw new UserError(`Auto-aggregated survey job already exists for this configuration`, {
        initiativeId,
        jobType: this.jobType,
        existingJobId: exists._id,
      });
    }

    const job = await this.createJob(workflow, [createTask], AutoAggregatedSurveyTasks.Create);

    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          initiativeId: job.initiativeId,
          cause: e,
        })
      );
    });

    return {
      jobId: job._id,
      taskId: createTask.id,
    };
  }

  private async createAutoAggregatedSurvey(job: AutoAggregatedSurveyJob, task: CreateAutoAggregatedSurveyTask) {
    await this.startTask(job, task);

    const initiative = await Initiative.findById(task.data.initiativeId).orFail().exec();
    const user = await User.findById(job.userId).orFail().exec();
    // Only get child latest surveys
    const surveyIds = await this.surveyAggregator.getAutoAggregatedSurveyIds(initiative._id);

    if (isFalsyOrEmpty(surveyIds)) {
      throw new UserError('No surveys to aggregate', {
        debugMessage: 'Trying to create auto aggregated survey without surveyIds',
        surveyIds: surveyIds,
      });
    }
    const surveysToAggregate = await this.surveyAggregator.getSurveysForAggregation(surveyIds);
    const aggregatedSurvey = await this.surveyAggregator.createAggregatedSurvey({
      name: `${initiative.name} auto-aggregated survey`,
      type: SurveyType.AutoAggregation,
      aggregationMode: AggregationMode.Children,
      company: initiative,
      effectiveDate: new Date().toISOString(),
      sourceName: DefaultBlueprintCode,
      surveysToAggregate,
      utrvType: UtrvType.Actual,
      user,
    });

    this.logger.info(`Job is completed with all successful tasks`, {
      jobId: job._id,
      jobType: job.type,
      taskId: task.id,
      taskType: task.type,
      aggregatedSurvey: aggregatedSurvey._id,
    });

    task.status = TaskStatus.Completed;
    job.markModified('tasks');
    return job.save();
  }

  private async updateAutoAggregatedSurvey(job: AutoAggregatedSurveyJob, task: UpdateAutoAggregatedSurveyTask) {
    await this.startTask(job, task);
    if (!job.userId) {
      throw new UserError('UserId is missing', { jobId: job._id, taskId: task.id });
    }

    const user = await User.findById(new ObjectId(job.userId)).orFail().exec();
    const initiative = await Initiative.findById(job.initiativeId).orFail().exec();
    const survey = await SurveyRepository.findOne({ initiativeId: initiative._id, type: SurveyType.AutoAggregation });
    if (!survey) {
      throw new ContextError(`Cannot find auto aggregated survey with initiativeId: ${initiative._id.toString()}`, {
        jobId: job._id,
        taskId: task.id,
      });
    }

    const updatedSurvey = await this.surveyAggregator.preUpdateAggregatedSurvey(survey, initiative._id);
    const result = await this.surveyAggregator.updateAggregatedSurvey(updatedSurvey, initiative, user);

    await this.sendUpdateAutoAggregatedSurveyNotification({
      userId: user._id,
      initiativeId: initiative._id,
      surveyId: survey._id,
    });

    this.logger.info(`Job ${job.type} is completed with all successful tasks`, {
      jobId: job._id,
      jobType: job.type,
      taskId: task.id,
      taskType: task.type,
      surveyId: result._id,
    });

    task.status = TaskStatus.Completed;
    job.markModified('tasks');
    return job.save();
  }

  public async createUpdateWorkflow(workflow: WorkflowUpdate): Promise<JobInfo> {
    const { initiativeId, surveyId } = workflow;

    await this.checkDidJobExist({ initiativeId });

    const taskId = generatedUUID();

    const task: UpdateAutoAggregatedSurveyTask = {
      id: taskId,
      name: 'Update auto aggregated survey',
      type: TaskType.UpdateAutoAggregatedSurvey,
      status: TaskStatus.Pending,
      data: { initiativeId, surveyId },
    };

    const job = await this.createJob(workflow, [task], AutoAggregatedSurveyTasks.Update);

    this.runJobInstantly(job, this.bgJobService);

    return { jobId: job._id, taskId: task.id };
  }

  private async sendUpdateAutoAggregatedSurveyNotification({
    userId,
    initiativeId,
    surveyId,
  }: {
    userId: ObjectId;
    initiativeId: ObjectId;
    surveyId: ObjectId;
  }) {
    const initiative = await Initiative.findById(initiativeId).exec();

    if (!initiative) {
      throw new ContextError('Trying to send a message with invalid initiativeId', { initiativeId });
    }
    const org = await this.rootInitiativeService.getOrganization(initiative);

    const customAttributes: CustomAttributes = {
      page: NotificationPage.SurveyOverview,
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      initiativeId: String(initiativeId),
      surveyId: String(surveyId),
      domain: undefined,
    };

    await this.notificationService.createNotification({
      title: 'Auto aggregated survey data update has been completed',
      content: `Click here to view survey`,
      category: NotificationCategory.Announcements,
      topic: `update-auto-aggregated-survey-${surveyId}`,
      customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: [{ id: String(userId) }],
    });
  }

  private async createJob(
    workflow: WorkflowCreate,
    tasks: (CreateAutoAggregatedSurveyTask | UpdateAutoAggregatedSurveyTask)[],
    type: AutoAggregatedSurveyTasks
  ) {
    const { initiativeId, user } = workflow;
    const jobId = new ObjectId();
    const key = this.getIdempotencyKey({
      id: initiativeId.toHexString(),
    });

    const createData: CreateJob = {
      _id: jobId,
      idempotencyKey: key,
      type: this.jobType,
      name: `${getCurrentDateStr()} ${type}`,
      tasks,
      logs: [createLogEntry(`Starting ${type} workflow`)],
      userId: user._id,
      initiativeId,
    };

    return (await BackgroundJob.create(createData)) as unknown as AutoAggregatedSurveyJob;
  }

  public async processTask(
    job: AutoAggregatedSurveyJob,
    task: CreateAutoAggregatedSurveyTask | UpdateAutoAggregatedSurveyTask
  ): Promise<TaskResult<AutoAggregatedSurveyJob>> {
    switch (task.type) {
      case TaskType.AutoAggregatedSurvey:
        return {
          job: await this.createAutoAggregatedSurvey(job, task),
          executeNextTask: true,
        };
      case TaskType.UpdateAutoAggregatedSurvey:
        return {
          job: await this.updateAutoAggregatedSurvey(job, task),
          executeNextTask: true,
        };
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }
}

let instance: AutoAggregatedSurveyWorkflow;
export const getAutoAggregatedSurveyWorkflow = () => {
  if (!instance) {
    instance = new AutoAggregatedSurveyWorkflow(
      wwgLogger,
      getBackgroundJobService(),
      getSurveyAggregator(),
      getNotificationService(),
      getRootInitiativeService()
    );
  }
  return instance;
};
