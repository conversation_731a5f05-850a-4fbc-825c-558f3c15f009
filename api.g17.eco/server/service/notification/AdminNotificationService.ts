import { ObjectId } from 'bson';
import Notification from '../../models/notification';
import NotificationSchedule from '../../models/notification-schedule';
import UserDailyNotification from '../../models/userDailyNotification';
import ScheduledNotification from '../../models/scheduledNotification';
import { type PipelineStage } from 'mongoose';
import type {
  SystemNotificationFilters,
  DailyNotificationFilters,
  ScheduledNotificationFilters,
  ScheduleFilters,
} from '../../routes/validation-schemas/notificationFilters';


interface BaseNotificationStats {
  total: number;
  pending: number;
  completed: number;
}
interface NotificationStats extends BaseNotificationStats {
  errored: number;
  skipped: number;
}

export class AdminNotificationService {
  /**
   * Build user search filter for searching by email, firstName, or surname
   */
  private buildUserSearchFilter(searchStr: string): PipelineStage {
    return {
      $match: {
        $or: [
          { 'user.email': { $regex: searchStr, $options: 'i' } },
          { 'user.firstName': { $regex: searchStr, $options: 'i' } },
          { 'user.surname': { $regex: searchStr, $options: 'i' } }
        ]
      }
    };
  }

  /**
   * Build userName concatenation field
   */
  private buildUserNameField(userFieldPrefix: string = '$user') {
    return {
      $concat: [
        { $ifNull: [`${userFieldPrefix}.firstName`, ''] },
        ' ',
        { $ifNull: [`${userFieldPrefix}.surname`, ''] }
      ]
    };
  }

  /**
   * Build initiative lookup pipeline stage
   */
  private buildInitiativeLookup(localField: string = 'initiativeId'): PipelineStage {
    return {
      $lookup: {
        from: 'initiatives',
        localField,
        foreignField: '_id',
        pipeline: [
          { $project: { name: 1, code: 1, permissionGroup: 1 } }
        ],
        as: 'initiativeData'
      }
    };
  }

  /**
   * Build safe ObjectId conversion for string IDs
   */
  private buildSafeObjectId(field: string) {
    return {
      $cond: {
        if: {
          $and: [
            { $ne: [field, null] },
            { $ne: [field, undefined] }
          ]
        },
        then: { $toObjectId: field },
        else: null
      }
    };
  }

  /**
   * Build status conditions for daily notifications based on status filter
   */
  private buildDailyNotificationStatusConditions(status?: string): Record<string, unknown> {
    switch (status) {
      case 'completed':
        return { completedDate: { $exists: true } };
      case 'errored':
        return { erroredDate: { $exists: true } };
      case 'skipped':
        return { skippedDate: { $exists: true } };
      case 'pending':
        return {
          completedDate: { $exists: false },
          erroredDate: { $exists: false },
          skippedDate: { $exists: false }
        };
      default:
        return {};
    }
  }

  /**
   * Build status conditions for scheduled notifications based on status filter
   */
  private buildScheduledNotificationStatusConditions(status?: string): Record<string, unknown> {
    switch (status) {
      case 'completed':
        return { completedDate: { $exists: true } };
      case 'pending':
        return {
          completedDate: { $exists: false },
          deletedDate: { $exists: false }
        };
      default:
        return {};
    }
  }

  /**
   * Build status conditions for notification schedules based on status filter
   */
  private buildScheduleStatusConditions(status?: string): Record<string, unknown> {
    switch (status) {
      case 'completed':
        return { completedDate: { $exists: true } };
      case 'pending':
        return { completedDate: { $exists: false } };
      default:
        return {};
    }
  }
  /**
   * Get system notifications (from notifications collection)
   * These are the main notification records sent to users with recipients
   */
  async getSystemNotifications(filters: SystemNotificationFilters) {
    const { category, userId, startDate, endDate, limit = 50, skip = 0, searchStr } = filters;

    // Build all match conditions once
    const matchConditions = {
      ...(searchStr && { title: { $regex: searchStr, $options: 'i' } }),
      ...(category && { category }),
      ...(userId && { 'recipients.userId': new ObjectId(userId) }),
      ...this.buildDateRangeCondition('created', startDate, endDate)
    };

    const pipeline: PipelineStage[] = [
      { $match: matchConditions },
      { $sort: { created: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'users',
          localField: 'recipients.userId',
          foreignField: '_id',
          pipeline: [
            { $project: { email: 1, firstName: 1, surname: 1 } }
          ],
          as: 'recipientUsers'
        }
      },
      {
        $addFields: {
          orgObjectId: this.buildSafeObjectId('$customAttributes.orgId'),
          initiativeObjectId: this.buildSafeObjectId('$customAttributes.initiativeId')
        }
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'orgObjectId',
          foreignField: '_id',
          pipeline: [
            { $project: { name: 1, code: 1 } }
          ],
          as: 'organizationData'
        }
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'initiativeObjectId',
          foreignField: '_id',
          pipeline: [
            { $project: { name: 1, code: 1 } }
          ],
          as: 'initiativeData'
        }
      },
      {
        $project: {
          _id: 1,
          title: 1,
          content: 1,
          category: 1,
          created: 1,
          customAttributes: 1,
          organization: { $arrayElemAt: ['$organizationData', 0] },
          initiative: { $arrayElemAt: ['$initiativeData', 0] },
          recipients: {
            $map: {
              input: '$recipients',
              as: 'recipient',
              in: {
                $let: {
                  vars: {
                    user: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: '$recipientUsers',
                            cond: { $eq: ['$$this._id', '$$recipient.userId'] }
                          }
                        },
                        0
                      ]
                    }
                  },
                  in: {
                    userId: '$$recipient.userId',
                    status: '$$recipient.status',
                    email: '$$user.email',
                    userName: this.buildUserNameField('$$user')
                  }
                }
              }
            }
          }
        }
      },
      {
        $project: {
          recipientUsers: 0,
          organizationData: 0,
          initiativeData: 0,
          orgObjectId: 0,
          initiativeObjectId: 0
        }
      }
    ];

    const [notifications, stats] = await Promise.all([
      Notification.aggregate(pipeline).exec(),
      this.getSystemNotificationStats(matchConditions)  // Pass matchConditions directly
    ]);

    return { data: notifications, stats };
  }

  /**
   * Get user daily notifications (from user-daily-notifications collection)
   * These are daily digest emails scheduled for users (e.g., survey deadlines)
   * Created from scheduled-notifications when they become due
   */
  async getUserDailyNotifications(filters: DailyNotificationFilters) {
    const { notificationType, userId, status, startDate, endDate, limit = 50, skip = 0, searchStr } = filters;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          ...(notificationType && { type: notificationType }),
          ...(userId && { userId: new ObjectId(userId) }),
          ...this.buildDailyNotificationStatusConditions(status),
          ...this.buildDateRangeCondition('scheduledDate', startDate, endDate)
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
      // Apply search filter on joined user data if searchStr provided
      ...(searchStr ? [this.buildUserSearchFilter(searchStr)] : []),
      { $sort: { scheduledDate: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $addFields: {
          userEmail: '$user.email',
          userName: this.buildUserNameField()
        }
      },
      { $project: { user: 0 } }
    ];

    const [notifications, stats] = await Promise.all([
      UserDailyNotification.aggregate(pipeline).exec(),
      this.getDailyNotificationStats(filters)
    ]);

    return { data: notifications, stats };
  }

  /**
   * Get scheduled notifications (from scheduled-notifications collection)
   * These are one-time scheduled notifications for initiatives (survey deadlines, reminders)
   * When due, they trigger creation of user-daily-notifications for affected users
   */
  async getScheduledNotifications(filters: ScheduledNotificationFilters) {
    const { notificationType, status, startDate, endDate, limit = 50, skip = 0 } = filters;

    // Build match conditions once
    const matchConditions = {
      ...(notificationType && { type: notificationType }),
      ...this.buildScheduledNotificationStatusConditions(status),
      ...this.buildDateRangeCondition('scheduledDate', startDate, endDate)
    };

    const pipeline: PipelineStage[] = [
      { $match: matchConditions },
      { $sort: { scheduledDate: -1 } },
      { $skip: skip },
      { $limit: limit },
      this.buildInitiativeLookup('initiativeId'),
      {
        $lookup: {
          from: 'surveys',
          localField: 'data.surveyId',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                name: 1,
                effectiveDate: 1,
                initiativeId: 1,
                completedDate: 1,
                deletedDate: 1,
                deadlineDate: 1,
                scheduledDates: 1
              }
            }
          ],
          as: 'surveyData'
        }
      },
      {
        $addFields: {
          initiative: { $arrayElemAt: ['$initiativeData', 0] },
          survey: { $arrayElemAt: ['$surveyData', 0] }
        }
      },
      { $project: { initiativeData: 0, surveyData: 0 } }
    ];

    const [notifications, stats] = await Promise.all([
      ScheduledNotification.aggregate(pipeline).exec(),
      this.getScheduledNotificationStats(matchConditions)  // Pass matchConditions directly
    ]);

    return { data: notifications, stats };
  }

  /**
   * Get notification schedules (from notification-schedules collection)
   * Email Summary System - NOT a simple queue:
   * 1. Accumulates notifications for users who have enabled email summaries in their preferences
   * 2. Groups notifications by user + initiative to create periodic summary emails
   * 3. Sends summary emails based on user preferences (hourly, daily, weekly, monthly, yearly)
   * 4. NOT related to user-daily-notifications - they are separate systems
   * Once sent, completedDate is set marking the summary as delivered
   */
  async getNotificationSchedules(filters: ScheduleFilters) {
    const { userId, status, startDate, endDate, limit = 50, skip = 0, searchStr } = filters;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          ...(userId && { userId: new ObjectId(userId) }),
          ...this.buildScheduleStatusConditions(status),
          ...this.buildDateRangeCondition('created', startDate, endDate)
        }
      },
      {
        $addFields: {
          sortOrder: {
            $cond: [
              { $eq: [{ $type: '$completedDate' }, 'missing'] },
              0,
              1
            ]
          },
          sortDate: {
            $cond: [
              { $eq: [{ $type: '$completedDate' }, 'missing'] },
              '$created',
              '$completedDate'
            ]
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
      // Apply search filter on joined user data if searchStr provided
      ...(searchStr ? [this.buildUserSearchFilter(searchStr)] : []),
      { $sort: { sortOrder: 1, sortDate: -1 } },
      { $project: { sortOrder: 0, sortDate: 0 } },
      { $skip: skip },
      { $limit: limit },
      this.buildInitiativeLookup('initiativeId'),
      {
        $lookup: {
          from: 'notifications',
          localField: 'notificationIds',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                _id: 1,
                title: 1,
                content: 1,
                category: 1,
                type: 1,
                created: 1,
                scheduledDate: 1,
                completedDate: 1,
                customAttributes: 1,
                recipients: 1
              }
            }
          ],
          as: 'notifications'
        }
      },
      {
        $addFields: {
          userEmail: '$user.email',
          userName: this.buildUserNameField(),
          initiative: { $arrayElemAt: ['$initiativeData', 0] }
        }
      },
      { $project: { user: 0, initiativeData: 0 } }
    ];

    const [schedules, stats] = await Promise.all([
      NotificationSchedule.aggregate(pipeline).exec(),
      this.getScheduleStats(filters)
    ]);

    return { data: schedules, stats };
  }


  // Statistics methods for each type
  private async getSystemNotificationStats(matchConditions: Record<string, unknown>) {
    const pipeline: PipelineStage[] = [
      { $match: matchConditions },
      {
        $group: {
          _id: { $ifNull: ['$category', 'uncategorized'] },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$count' },
          categories: {
            $push: {
              k: '$_id',
              v: '$count'
            }
          }
        }
      },
      {
        $project: {
          total: 1,
          categories: {
            $arrayToObject: '$categories'
          }
        }
      }
    ];

    const result = await Notification.aggregate<{ total: number, categories: Record<string, number> }>(pipeline).exec();

    return result[0] || { total: 0, categories: {} };
  }

  private async getDailyNotificationStats(filters: DailyNotificationFilters) {
    const { notificationType, userId, status, startDate, endDate, searchStr } = filters;

    const pipeline: PipelineStage[] = [];

    const matchConditions: Record<string, unknown> = {
      ...(notificationType && { type: notificationType }),
      ...(userId && { userId: new ObjectId(userId) }),
      ...this.buildDailyNotificationStatusConditions(status),
      ...this.buildDateRangeCondition('scheduledDate', startDate, endDate)
    };

    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions });
    }

    // If searchStr provided, need to lookup users and filter
    if (searchStr) {
      pipeline.push(
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
        this.buildUserSearchFilter(searchStr)
      );
    }

    pipeline.push({
      $group: {
        _id: null,
        total: { $sum: 1 },
        pending: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: [{ $type: '$completedDate' }, 'missing'] },
                  { $eq: [{ $type: '$erroredDate' }, 'missing'] },
                  { $eq: [{ $type: '$skippedDate' }, 'missing'] }
                ]
              },
              1,
              0
            ]
          }
        },
        completed: {
          $sum: { $cond: [{ $ne: [{ $type: '$completedDate' }, 'missing'] }, 1, 0] }
        },
        errored: {
          $sum: { $cond: [{ $ne: [{ $type: '$erroredDate' }, 'missing'] }, 1, 0] }
        },
        skipped: {
          $sum: { $cond: [{ $ne: [{ $type: '$skippedDate' }, 'missing'] }, 1, 0] }
        }
      }
    });

    const [result] = await UserDailyNotification.aggregate<NotificationStats>(pipeline).exec();

    return result || { total: 0, pending: 0, completed: 0, errored: 0, skipped: 0 };
  }

  private async getScheduledNotificationStats(matchConditions: Record<string, unknown>) {
    const pipeline: PipelineStage[] = [
      { $match: matchConditions },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          pending: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: [{ $type: '$completedDate' }, 'missing'] },
                    { $eq: [{ $type: '$deletedDate' }, 'missing'] }
                  ]
                },
                1,
                0
              ]
            }
          },
          completed: {
            $sum: {
              $cond: [
                { $ne: [{ $type: '$completedDate' }, 'missing'] },
                1,
                0
              ]
            }
          }
        }
      }
    ];

    const [result] = await ScheduledNotification.aggregate<BaseNotificationStats>(pipeline).exec();

    return result || { total: 0, pending: 0, completed: 0 };
  }

  private async getScheduleStats(filters: ScheduleFilters) {
    const { userId, status, startDate, endDate, searchStr } = filters;

    const pipeline: PipelineStage[] = [];
    const matchConditions: Record<string, unknown> = {
      ...(userId && { userId: new ObjectId(userId) }),
      ...this.buildScheduleStatusConditions(status),
      ...this.buildDateRangeCondition('created', startDate, endDate)
    };

    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions });
    }

    // If searchStr provided, need to lookup users and filter
    if (searchStr) {
      pipeline.push(
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
        this.buildUserSearchFilter(searchStr)
      );
    }

    pipeline.push({
      $group: {
        _id: null,
        total: { $sum: 1 },
        pending: {
          $sum: {
            $cond: [
              { $eq: [{ $type: '$completedDate' }, 'missing'] },
              1,
              0
            ]
          }
        },
        completed: {
          $sum: {
            $cond: [
              { $ne: [{ $type: '$completedDate' }, 'missing'] },
              1,
              0
            ]
          }
        }
      }
    });

    const result = await NotificationSchedule.aggregate<NotificationStats>(pipeline).exec();

    if (result.length === 0) {
      return { total: 0, pending: 0, completed: 0 };
    }

    return result[0];
  }

  private buildDateFilter(startDate?: Date, endDate?: Date) {
    const filter: Record<string, unknown> = {};
    if (startDate) {
      filter.$gte = new Date(startDate);
    }
    if (endDate) {
      filter.$lte = new Date(endDate);
    }
    return Object.keys(filter).length > 0 ? filter : undefined;
  }

  private buildDateRangeCondition(fieldName: string, startDate?: Date, endDate?: Date): Record<string, unknown> | undefined {
    const dateFilter = this.buildDateFilter(startDate, endDate);
    return dateFilter ? { [fieldName]: dateFilter } : undefined;
  }
}

// Singleton instance
let instance: AdminNotificationService;

export const getAdminNotificationService = (): AdminNotificationService => {
  if (!instance) {
    instance = new AdminNotificationService();
  }
  return instance;
};
