import type { ObjectId } from 'bson';
import type { NotApplicableTypes, UniversalTrackerValueExtended } from '../../models/universalTrackerValue';
import type { InitiativePlain } from '../../models/initiative';
import type { AIModelType } from './AIModelFactory';
import type { RowData, UniversalTrackerValuePublic } from '../../models/public/universalTrackerValueType';
import type { UtrvPromptInput } from './utrv-assistant/types';

// Enum for supported AI providers
export enum AiProvider {
  OpenAi = 'openai',
  Claude = 'claude',
  Gemini = 'gemini'
}

export type AIUtrvSuggestion = {
  predictedAnswer?: string | number | { [key: string]: string | number | string[] };
  questionExplanation: string;
  bestPractice: string[];
  keyInfo: string[];
  suggestedEvidence: {
    primaryDocumentation: string[];
    supportingDocumentation: string[];
  };
  whereToFind: {
    externalSource: string[];
    internalSource: string[];
  };
};

export enum RefineAction {
  Rewrite = 'rewrite',
  Shorten = 'shorten',
}

export interface GetFurtherNotesParams {
  utrv: UniversalTrackerValueExtended;
  initiative: InitiativePlain;
  draftData: Pick<UniversalTrackerValuePublic, 'value' | 'unit' | 'numberScale' | 'valueData'>;
  userId: ObjectId;
  modelType?: AIModelType;
}

export interface RefineFurtherNotesParams extends GetFurtherNotesParams {
  textToRefine: string;
  action: RefineAction;
  additionalContext: string | undefined;
}

export interface FurtherNotesDataContext {
  title: string;
  question: string;
  description: string | undefined;
  answers:
    | NotApplicableTypes
    | {
        value: number | null;
        unit?: string;
        numberScale?: string;
        description: string | null;
        tableData: Record<string, RowData[]>;
        previousUtrvs?: UtrvPromptInput['previousUtrvs'];
      };
}
