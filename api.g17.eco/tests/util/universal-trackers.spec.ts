/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { universalTrackerOne, utrTableOne } from '../fixtures/universalTrackerFixtures';
import { isPercentageColumn, isSingleRowTable, mergeUtrOverrides, mergeUtrValueValidation } from '../../server/util/universal-trackers';
import { expect } from 'chai';
import { ColumnType, UtrValueType, ValueValidation } from '../../server/models/public/universalTrackerType';
import { ObjectId } from 'bson';
import { utrvNumericValueListOne } from '../fixtures/universalTrackerValueFixtures';
import { getExportedValue, getFormattedValue, getRawValue } from '../../server/service/custom-report/utils';

describe('universal tracker utils', () => {

  describe('mergeUtrValueValidation', () => {

    const name = 'OLD NAME, NOT MERGE';
    const tableWithDecimal = {
      table: {
        columns: [
          { type: ColumnType.Number, code: 'col1', name },
          { type: ColumnType.Number, code: 'col2', name, validation: { decimal: 2 } },
          { type: ColumnType.Text, code: 'col3', name },
        ],
      }
    } satisfies ValueValidation

    const tableWithOverrides = {
      table: {
        columns: [
          { type: ColumnType.Number, code: 'col1', name, numberScaleInput: 'thousands' },
          { type: ColumnType.Number, code: 'col2', name, unitInput: 'kJ' },
          { type: ColumnType.Text, code: 'col3', name },
        ],
      },
    } satisfies ValueValidation;

    it('should keep original type', () => {
      const result = mergeUtrValueValidation({
        utr: { _id: new ObjectId(), valueValidation: { decimal: 2 }, valueType: UtrValueType.Number },
        initiativeUtr: undefined
      });
      expect(result).to.deep.equals({ decimal: 2 });
    });

    it('should return original utr validation if no decimal', () => {
      const utr = { _id: new ObjectId(), valueValidation: {}, valueType: UtrValueType.Number }
      const result = mergeUtrValueValidation({
        utr,
        initiativeUtr: { _id: new ObjectId(), universalTrackerId: utr._id, valueValidation: {} },
      });
      expect(result).eq(utr.valueValidation);
    });

    it('should merge and use override decimal', () => {
      const utrId = new ObjectId();
      const result = mergeUtrValueValidation({
        utr: { _id: utrId, valueValidation: { decimal: 2 }, valueType: UtrValueType.Number },
        initiativeUtr: { _id: new ObjectId(), universalTrackerId: utrId,  valueValidation: { decimal: 3 } },
      });
      expect(result).to.be.deep.equals({ decimal: 3 });
    });

    it('should merge and use override decimal', () => {
      const result = mergeUtrValueValidation({
        utr: utrvNumericValueListOne.universalTracker,
        initiativeUtr: {
          _id: new ObjectId(),
          universalTrackerId: utrvNumericValueListOne.universalTrackerId,
          valueValidation: { decimal: 3 },
        },
      });
      expect(result).to.be.deep.equals({
        valueList: utrvNumericValueListOne.universalTracker.valueValidation?.valueList,
        decimal: 3,
      });
    });

    it('should return original utr validation', () => {
      const result = mergeUtrValueValidation({ utr: utrTableOne, initiativeUtr: undefined });
      expect(result).eq(utrTableOne.valueValidation);
    });

    it('should merge with value only using validation props', () => {
      const result = mergeUtrValueValidation({
        utr: utrTableOne,
        initiativeUtr: { _id: new ObjectId(), universalTrackerId: utrTableOne._id, valueValidation: tableWithDecimal },
      });
      // Change for table
      const [col1, col2, col3] = result?.table?.columns ?? []
      expect(col1).to.be.deep.eq({
        type: ColumnType.Number,
        code: 'col1',
        name: 'Col1 Name',
      });
      expect(col2).to.be.deep.eq({
        type: ColumnType.Number,
        code: 'col2',
        name: 'Col2 Name',
        validation: { decimal: 2 }
      });
      expect(col3).to.be.deep.eq({ type: ColumnType.Text, code: 'col3', name: 'Col3 Name' });
    });

    it('should merge unitInput and numberScaleInput if have', () => {
      const result = mergeUtrValueValidation({
        utr: utrTableOne,
        initiativeUtr: { _id: new ObjectId(), universalTrackerId: utrTableOne._id, valueValidation: tableWithOverrides },
      });
      // Change for table
      const [col1, col2, col3] = result?.table?.columns ?? [];
      expect(col1).to.be.deep.eq({
        type: ColumnType.Number,
        code: 'col1',
        name: 'Col1 Name',
        numberScaleInput: 'thousands',
      });
      expect(col2).to.be.deep.eq({
        type: ColumnType.Number,
        code: 'col2',
        name: 'Col2 Name',
        unitInput: 'kJ'
      });
      expect(col3).to.be.deep.eq({ type: ColumnType.Text, code: 'col3', name: 'Col3 Name' });
    });

    it('should return original utr validation', () => {
      const result = mergeUtrValueValidation({
        utr: utrTableOne,
        initiativeUtr: { _id: new ObjectId(), universalTrackerId: utrTableOne._id, valueValidation: { decimal: 10 } },
      });
      // Change for table
      expect(result).to.be.deep.eq(utrTableOne.valueValidation);
    });
  });

  describe('getExportedValue fn', () => {


    const testCases = [
      {
        title: 'all undefined',
        row: { value: undefined },
        expected: undefined,
      },
      {
        title: 'undefined and empty values',
        row: { value: undefined, values: [] },
        expected: '',
      },
      {
        title: 'string values',
        row: { value: '2 USD' },
        expected: '2 USD',
      },
      {
        title: 'value is array of strings',
        row: { value: ['one', 'two'] },
        expected: 'one, two',
      },
      {
        title: 'values empty',
        row: { value: undefined, values: [] },
        expected: '',
      },
      {
        title: 'values empty',
        row: { value: undefined, values: ['1'] },
        expected: '1',
      },
      {
        title: 'values with string and number',
        row: { value: undefined, values: ['1', '3'] },
        expected: '1, 3',
      },
      {
        title: 'values with string and number',
        row: { value: undefined, values: [3, 4] },
        expected: '3, 4',
      },
    ]

    testCases.forEach(({ title, row, expected }) => {
      it(title, () => {
        expect(getExportedValue(row)).eq(expected)
      });
    })
  });


  describe('getFormattedValue fn', () => {

    it('should handle undefined', () => {
      expect(getFormattedValue(undefined, undefined)).eq(undefined);
    });

    it('should handle undefined with decimal', () => {
      expect(getFormattedValue(undefined, 2)).eq(undefined);
    });

    it('should handle string', () => {
      expect(getFormattedValue('2', 2)).eq('2.00');
    });

    it('should handle cell', () => {
      const cell = { value: 0, options: { type: 'n', format: '0.00' } };
      expect(getFormattedValue(cell, 2)).eqls(cell);
    });
  })


  describe('getRawValue', () => {
    const cell = { value: 0, options: { type: 'n', format: '0.00' } };
    const dataProvider = [
      [1, 1],
      [undefined, undefined],
      [cell, cell.value],
      ['2', '2'],
      ['', ''],
    ]

    dataProvider.forEach(([input, expected]) => {
      const name = typeof input === 'object' ? 'cell' : input;
      it(`should extract value from ${name}`, () => {
        expect(getRawValue(input)).eq(expected);
      });
    });
  });

  describe('mergeUtrOverrides', () => {
    const initiativeUtrMinData = {
      _id: new ObjectId(),
      universalTrackerId: universalTrackerOne._id,
    };
    const simpleOverrideCases = [
      {
        label: 'no overrides',
        overrides: undefined,
        expectedOverrides: {},
      },
      {
        label: 'has unitInput',
        overrides: {
          ...initiativeUtrMinData,
          unitInput: 'kJ',
        },
        expectedOverrides: {
          unitInput: 'kJ',
        },
      },
      {
        label: 'has numberScaleInput',
        overrides: { ...initiativeUtrMinData, numberScaleInput: 'thousands' },
        expectedOverrides: { numberScaleInput: 'thousands' },
      },
      {
        label: 'has decimal',
        overrides: { ...initiativeUtrMinData, valueValidation: { decimal: 0 } },
        expectedOverrides: { valueValidation: { decimal: 0 } },
      },
      {
        label: 'has all overrides',
        overrides: {
          ...initiativeUtrMinData,
          unitInput: 'kJ',
          numberScaleInput: 'thousands',
          valueValidation: { decimal: 1 },
        },
        expectedOverrides: {
          unitInput: 'kJ',
          numberScaleInput: 'thousands',
          valueValidation: {
            decimal: 1,
          },
        },
      },
    ];

    const simpleTestCases = [UtrValueType.Number, UtrValueType.Percentage, UtrValueType.NumericValueList]
      .map((valueType) =>
        simpleOverrideCases.map(({ label, overrides, expectedOverrides }) => ({
          title: `${valueType} - ${label}`,
          utr: { _id: universalTrackerOne._id, valueType },
          initiativeUtr: overrides,
          expectedOverrides,
        }))
      )
      .flat();

    simpleTestCases.forEach(({ title, utr, initiativeUtr, expectedOverrides }) => {
      it(title, () => {
        const result = mergeUtrOverrides({
          utr,
          initiativeUtr,
        });
        expect(result).to.be.deep.eq(expectedOverrides);
      });
    });
  });

  describe('isPercentageColumn', () => {
    it('should return true for a column with type "percentage"', () => {
      const column = { type: UtrValueType.Percentage, validation: { min: 0, max: 100 } };
      expect(isPercentageColumn(column)).to.be.true;
    });

    it('should return true for a column with type "number" and validation range 0-100', () => {
      const column = { type: UtrValueType.Number, validation: { min: 0, max: 100 } };
      expect(isPercentageColumn(column)).to.be.true;
    });

    it('should return false for a column with type "number" but different validation range', () => {
      const column = { type: UtrValueType.Number, validation: { min: 0, max: 99 } };
      expect(isPercentageColumn(column)).to.be.false;
    });

    it('should return false for a column with type "text"', () => {
      const column = { type: UtrValueType.Text, validation: { min: 0, max: 100 } };
      expect(isPercentageColumn(column)).to.be.false;
    });

    it('should return false for a column without validation', () => {
      const column = { type: UtrValueType.Number };
      expect(isPercentageColumn(column)).to.be.false;
    });
  });
  
  describe('isSingleRowTable', () => {
    it('should return true for a single row table', () => {
      const utr = {
        ...universalTrackerOne,
        valueType: UtrValueType.Table,
        valueValidation: {
          table: {
            columns: [],
            validation: {
              maxRows: 1,
            },
          },
        },
      };
      expect(isSingleRowTable(utr)).to.be.true;
    });

    it('should return false for a multi-row table', () => {
      const utr = {
        ...universalTrackerOne,
        valueType: UtrValueType.Table,
        valueValidation: {
          table: {
            columns: [],
            validation: {
              maxRows: 2,
            },
          },
        },
      };
      expect(isSingleRowTable(utr)).to.be.false;
    });

    it('should return false when valueType is not Table', () => {
      const utr = {
        ...universalTrackerOne,
        valueType: UtrValueType.Number,
      };
      expect(isSingleRowTable(utr)).to.be.false;
    });

    it('should return false when validation is undefined', () => {
      const utr = {
        valueType: UtrValueType.Table,
        valueValidation: undefined,
      };
      expect(isSingleRowTable(utr)).to.be.false;
    });
  });
});
