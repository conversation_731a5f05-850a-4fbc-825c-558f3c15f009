/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import '../../setup';
import { expect } from 'chai';
import { SurveyAggregator } from '../../../server/service/survey/SurveyAggregator';
import {
  AggregatedUniversalTracker,
  DisaggregationUniversalTrackerValueFields,
} from '../../../server/service/utr/aggregation/AggregatedUniversalTracker';
import { initiativeOneSimple, initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { createUtr, createUtrFromCode } from '../../fixtures/universalTrackerFixtures';
import { createSandbox, SinonStub } from 'sinon';
import { UniversalTrackerRepository } from '../../../server/repository/UniversalTrackerRepository';
import { getBlueprintRepository } from '../../../server/repository/BlueprintRepository';
import { Blueprints } from '../../../server/survey/blueprints';
import { userOne } from '../../fixtures/userFixtures';
import UniversalTracker, { UniversalTrackerModel } from '../../../server/models/universalTracker';
import { ObjectId } from 'bson';
import { ActionList, DataPeriods, UtrvType } from '../../../server/service/utr/constants';
import { SurveyRepository } from '../../../server/repository/SurveyRepository';
import { SurveyAggregateProcess } from '../../../server/service/survey/SurveyAggregateProcess';
import { createSurveyComposer } from '../../../server/service/survey/SurveyComposer';
import { gri2020 } from '../../../server/survey/gri/gri2020';
import Survey, { SourceItemType, SurveyModelPlain, SurveyType } from '../../../server/models/survey';
import { SurveyConflictVerification } from '../../../server/service/survey/SurveyConflictVerification';
import { createSurveyManager } from '../../../server/service/survey/SurveyManager';
import { UtrValueType, ValueAggregation } from '../../../server/models/public/universalTrackerType';
import { createUtrv as baseCreateUtrv, hydrateLatestHistory } from '../../fixtures/compositeUTRVFixtures';
import { createMongooseModel } from '../../setup';
import { surveyOne } from "../../fixtures/survey";
import { SurveyProcessRepository } from '../../../server/repository/SurveyProcessRepository';
import { InitiativeRepository } from '../../../server/repository/InitiativeRepository';
import UserError from '../../../server/error/UserError';
import { SurveyScope } from '../../../server/service/survey/SurveyScope';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { createHistory } from "../../factories/valueHistory";
import { blueprintDefaultUnitConfig } from '../../../server/service/units/unitTypes';


describe('SurveyAggregator', function () {
  const blueprintRepository = getBlueprintRepository();
  const surveyComposer = createSurveyComposer(blueprintRepository);
  const surveyManager = createSurveyManager();
  const surveyAggregator = new SurveyAggregator(blueprintRepository, surveyComposer, surveyManager, wwgLogger);

  const sandbox = createSandbox();

  const createUtrv: typeof baseCreateUtrv = (id, utrId, value, overrides, iId) => {
    return baseCreateUtrv(
      id,
      utrId,
      value,
      {
        history: [
          createHistory({ action: ActionList.Created }),
          createHistory({ action: ActionList.Updated, value }),
        ],
        ...overrides,
      },
      iId,
    );
  }

  describe('convertToUtrvExtended', function () {
    before(() => {
      sandbox.stub(UniversalTrackerRepository, 'find').resolves([]);
    });

    after(() => {
      sandbox.restore();
    });

    it('should work with empty utrvs', function () {
      const utr = createUtrFromCode();
      const utrvs: DisaggregationUniversalTrackerValueFields[] = [];
      const aggregatedValue = new AggregatedUniversalTracker(utr, utrvs);
      const result = surveyAggregator.convertToUtrvExtended(aggregatedValue, initiativeOneSimpleId);
      expect(result.universalTracker).to.be.eq(utr);
      expect(result.value).to.be.eq(undefined);
      expect(result.sourceItems).to.have.lengthOf(0);
    });

    it('should work with utrvs', function () {
      const utr = createUtr(new ObjectId(), 'random-two', { valueType: UtrValueType.Number });
      const value = Math.random();
      const updatedHistory = createHistory({ 'action': ActionList.Updated, value, });
      const utrv = createUtrv(new ObjectId(), utr._id, value, {
        history: [createHistory({ action: ActionList.Created, value: undefined }), updatedHistory],
      });
      const extendedUtrvs: DisaggregationUniversalTrackerValueFields[] = [{
        ...utrv,
        latestHistory: updatedHistory,
        updatedHistory
      }];
      const aggregatedValue = new AggregatedUniversalTracker(utr, extendedUtrvs);

      const result = surveyAggregator.convertToUtrvExtended(aggregatedValue, initiativeOneSimpleId);
      expect(result.universalTracker).to.be.eq(utr);
      expect(result.value).to.be.eq(value);
      expect(result.sourceItems).to.have.lengthOf(1);
      expect(result.valueAggregation).to.be.eq(ValueAggregation.ValueSumAggregator);
    });
  });

  describe('createAggregatedSurvey-method', function () {
    const effectiveDate = '2021-04-04T14:00:00.000Z';
    const surveyId = new ObjectId();

    let getUtrvsStub: SinonStub;
    let importerStub: SinonStub;

    before(() => {
      sandbox.stub(SurveyProcessRepository, 'loadSurveyUtrvs').resolves([]);
      sandbox.stub(UniversalTracker, 'findOne').callsFake((query) => {
        return createMongooseModel({ _id: new ObjectId(), code: (query as { code?: string })?.code || 'unknown' })
      });
      sandbox.stub(InitiativeRepository, 'getAllChildrenById').resolves([initiativeOneSimple]);

      sandbox.stub(surveyComposer, 'composeBlueprint').resolves(gri2020);
      sandbox.stub(surveyManager, 'processAction').resolves(); // Don't care about this for this test
      sandbox.stub(SurveyRepository, 'findSurveys').resolves([
        {
          _id: surveyId,
          initiativeId: initiativeOneSimple._id,
          effectiveDate: new Date(effectiveDate),
          scope: undefined,
        },
      ] as SurveyModelPlain[]);
      sandbox.stub(SurveyConflictVerification, 'verify');
      importerStub = sandbox.stub(SurveyAggregateProcess, 'createSurveyAggregatedProcess');
      importerStub.callsFake(async (blueprint, user, survey, nodes) => {
        const surveyProcess = new SurveyAggregateProcess(blueprint, user, survey, nodes);
        sandbox.stub(surveyProcess, 'saveUpdate').resolves(survey);
        return surveyProcess;
      });

      const utr = createUtr(new ObjectId(), 'survey/generic/ebitda', { valueType: UtrValueType.Number, valueAggregation: ValueAggregation.ValueAverageAggregator });
      sandbox.stub(UniversalTrackerRepository, 'find').resolves([utr as UniversalTrackerModel]);

      getUtrvsStub = sandbox.stub(UniversalTrackerRepository, 'getSurveyUtrvs');
      getUtrvsStub.resolves([]);
    });

    after(() => sandbox.restore());

    it('should work with no data utrvs', async function () {
      const aggregatedSurveyIds = [surveyId].map((id) => new ObjectId(id));
      const surveysToAggregate = await surveyAggregator.getSurveysForAggregation(aggregatedSurveyIds);

      const survey = await surveyAggregator.createAggregatedSurvey({
        name: 'Test Aggregated One',
        company: initiativeOneSimple,
        type: SurveyType.Aggregation,
        effectiveDate,
        sourceName: Blueprints.Gri2020,
        surveysToAggregate,
        utrvType: UtrvType.Actual,
        user: userOne,
      });
      expect(getUtrvsStub.calledOnce).to.be.true;
      expect(importerStub.calledOnce).to.be.true;
      expect(survey.isNew).to.be.true;
    });
  });

  describe('getAggregatedData', function () {

    const sandbox = createSandbox();

    const rootInitiativeId = initiativeOneSimple._id;
    const childAInitiativeId = new ObjectId();
    const childBInitiativeId = new ObjectId();
    const grandchildA1InitiativeId = new ObjectId();
    const grandchildA2InitiativeId = new ObjectId();
    const grandchildB1InitiativeId = new ObjectId();
    const grandchildB2InitiativeId = new ObjectId();

    const utr = createUtr(new ObjectId(), 'survey/generic/ebitda', { valueType: UtrValueType.Number, valueAggregation: ValueAggregation.ValueAverageAggregator });

    before(() => {
      sandbox.stub(SurveyProcessRepository, 'loadSurveyUtrvs').resolves([]);
      sandbox.stub(UniversalTrackerRepository, 'find').resolves([utr as UniversalTrackerModel]);
      sandbox.stub(InitiativeRepository, 'getAllChildrenById').resolves([{
        _id: rootInitiativeId,
        parentId: null,
      },
      {
        _id: childAInitiativeId,
        parentId: rootInitiativeId,
      },
      {
        _id: grandchildA1InitiativeId,
        parentId: childAInitiativeId,
      },
      {
        _id: grandchildA2InitiativeId,
        parentId: childAInitiativeId,
      },
      {
        _id: childBInitiativeId,
        parentId: rootInitiativeId,
      },
      {
        _id: grandchildB1InitiativeId,
        parentId: childBInitiativeId,
      },
      {
        _id: grandchildB2InitiativeId,
        parentId: childBInitiativeId,
      }]);

    });

    after(() => sandbox.restore());

    it('Example A: One report per Grandchild', async function () {

      const expandedBlueprint = await blueprintRepository.getExpandedBlueprintByCode(Blueprints.Gri2020);

      if (!expandedBlueprint) {
        throw new Error('Failed to get expanded blueprint');
      }

      const surveyA1Id = new ObjectId();
      const surveyA2OctId = new ObjectId();
      const surveyB1Id = new ObjectId();
      const surveyB2OctId = new ObjectId();

      const surveyA1 = { ...surveyOne, name: 'Grandchild Survey A1', _id: surveyA1Id, initiativeId: grandchildA1InitiativeId };
      const surveyA2Oct = { ...surveyOne, name: 'Grandchild Survey A2 - Oct', _id: surveyA2OctId, initiativeId: grandchildA2InitiativeId };
      const surveyB1  = { ...surveyOne, name: 'Grandchild Survey B1',  _id: surveyB1Id, initiativeId: grandchildB1InitiativeId };
      const surveyB2Oct  = { ...surveyOne, name: 'Grandchild Survey B2 - Oct',  _id: surveyB2OctId, initiativeId: grandchildB2InitiativeId };

      const getSurveyUtrvs = sandbox.stub(UniversalTrackerRepository, 'getSurveyUtrvs').callsFake(async ({ surveyIds }) => {
        const utrvs = [];
        if (surveyIds.includes(surveyA1Id)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA1InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyA2OctId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA2InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyB1Id)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB1InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyB2OctId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB2InitiativeId, utr._id, 10)));
        return [{
            universalTracker: utr, _id: new ObjectId(),
            utrvs
          }];
      });

      const utrvs = await surveyAggregator.getAggregatedData({
        expandedBlueprint,
        surveysToAggregate: [surveyA1, surveyA2Oct, surveyB1, surveyB2Oct],
        initiative: initiativeOneSimple,
        utrvStatuses: [ActionList.Verified],
      });

      expect(utrvs.length).to.be.eq(1);
      expect(utrvs[0].value).to.be.eq(40);
      expect(utrvs[0].sourceItems).to.have.lengthOf(4);
      getSurveyUtrvs.restore();

    });

    it('Example B: Multiple reports per Grandchild', async function () {

      const expandedBlueprint = await blueprintRepository.getExpandedBlueprintByCode(Blueprints.Gri2020);

      if (!expandedBlueprint) {
        throw new Error('Failed to get expanded blueprint');
      }

      const surveyA1Id = new ObjectId();
      const surveyA2OctId = new ObjectId();
      const surveyA2NovId = new ObjectId();
      const surveyB1Id = new ObjectId();
      const surveyB2OctId = new ObjectId();
      const surveyB2NovId = new ObjectId();

      const surveyA1 = { ...surveyOne, name: 'Grandchild Survey A1', _id: surveyA1Id, initiativeId: grandchildA1InitiativeId };
      const surveyA2Oct = { ...surveyOne, name: 'Grandchild Survey A2 - Oct', _id: surveyA2OctId, initiativeId: grandchildA2InitiativeId };
      const surveyA2Nov  = { ...surveyOne, name: 'Grandchild Survey A2 - Nov',  _id: surveyA2NovId, initiativeId: grandchildA2InitiativeId };
      const surveyB1  = { ...surveyOne, name: 'Grandchild Survey B1',  _id: surveyB1Id, initiativeId: grandchildB1InitiativeId };
      const surveyB2Oct  = { ...surveyOne, name: 'Grandchild Survey B2 - Oct',  _id: surveyB2OctId, initiativeId: grandchildB2InitiativeId };
      const surveyB2Nov  = { ...surveyOne, name: 'Grandchild Survey B2 - Nov',  _id: surveyB2NovId, initiativeId: grandchildB2InitiativeId };

      const getSurveyUtrvs = sandbox.stub(UniversalTrackerRepository, 'getSurveyUtrvs').callsFake(async ({ surveyIds }) => {
        const utrvs = [];
        if (surveyIds.includes(surveyA1Id)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA1InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyA2OctId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA2InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyA2NovId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA2InitiativeId, utr._id, 12)));
        if (surveyIds.includes(surveyB1Id)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB1InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyB2OctId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB2InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyB2NovId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB2InitiativeId, utr._id, 12)));
        return [{
            universalTracker: utr, _id: new ObjectId(),
            utrvs
          }];
      });

      const utrvs = await surveyAggregator.getAggregatedData({
        expandedBlueprint,
        surveysToAggregate: [surveyA1, surveyA2Oct, surveyA2Nov, surveyB1, surveyB2Oct, surveyB2Nov],
        initiative: initiativeOneSimple,
        utrvStatuses: [ActionList.Verified],
      });

      expect(utrvs.length).to.be.eq(1);
      expect(utrvs[0].value).to.be.eq(42);
      expect(utrvs[0].sourceItems).to.have.lengthOf(6);
      getSurveyUtrvs.restore();
    });

    it('Example C: Mix of Child and Grandchild reports', async function () {

      const expandedBlueprint = await blueprintRepository.getExpandedBlueprintByCode(Blueprints.Gri2020);

      if (!expandedBlueprint) {
        throw new Error('Failed to get expanded blueprint');
      }

      const surveyA1Id = new ObjectId();
      const surveyA2OctId = new ObjectId();
      const surveyA2NovId = new ObjectId();
      const surveyBId = new ObjectId();
      const surveyB1Id = new ObjectId();
      const surveyB2OctId = new ObjectId();
      const surveyB2NovId = new ObjectId();

      const surveyA1 = { ...surveyOne, name: 'Grandchild Survey A1', _id: surveyA1Id, initiativeId: grandchildA1InitiativeId };
      const surveyA2Oct = { ...surveyOne, name: 'Grandchild Survey A2 - Oct', _id: surveyA2OctId, initiativeId: grandchildA2InitiativeId };
      const surveyA2Nov  = { ...surveyOne, name: 'Grandchild Survey A2 - Nov',  _id: surveyA2NovId, initiativeId: grandchildA2InitiativeId };
      const surveyB  = { ...surveyOne, name: 'Child Survey B',  _id: surveyBId, initiativeId: childBInitiativeId };
      const surveyB1  = { ...surveyOne, name: 'Grandchild Survey B1',  _id: surveyB1Id, initiativeId: grandchildB1InitiativeId };
      const surveyB2Oct  = { ...surveyOne, name: 'Grandchild Survey B2 - Oct',  _id: surveyB2OctId, initiativeId: grandchildB2InitiativeId };
      const surveyB2Nov  = { ...surveyOne, name: 'Grandchild Survey B2 - Nov',  _id: surveyB2NovId, initiativeId: grandchildB2InitiativeId };

      const getSurveyUtrvs = sandbox.stub(UniversalTrackerRepository, 'getSurveyUtrvs').callsFake(async ({ surveyIds }) => {
        const utrvs = [];
        if (surveyIds.includes(surveyA1Id)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA1InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyA2OctId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA2InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyA2NovId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildA2InitiativeId, utr._id, 12)));
        if (surveyIds.includes(surveyBId)) utrvs.push(hydrateLatestHistory(createUtrv(childBInitiativeId, utr._id, 30)));
        if (surveyIds.includes(surveyB1Id)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB1InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyB2OctId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB2InitiativeId, utr._id, 10)));
        if (surveyIds.includes(surveyB2NovId)) utrvs.push(hydrateLatestHistory(createUtrv(grandchildB2InitiativeId, utr._id, 12)));
        return [{
            universalTracker: utr, _id: new ObjectId(),
            utrvs
          }];
      });

      const utrvs = await surveyAggregator.getAggregatedData({
        expandedBlueprint,
        surveysToAggregate: [surveyA1, surveyA2Oct, surveyA2Nov, surveyB, surveyB1, surveyB2Oct, surveyB2Nov],
        initiative: initiativeOneSimple,
        utrvStatuses: [ActionList.Verified],
      });

      expect(utrvs.length).to.be.eq(1);
      expect(utrvs[0].value).to.be.eq(51);
      expect(utrvs[0].sourceItems).to.have.lengthOf(4); // 3 of them get ignored
      getSurveyUtrvs.restore();
    });
  });

  describe('updateAggregatedSurveyConfig', () => {
    afterEach(() => sandbox.restore());

    it('should throw error if surveyIds are empty', async () => {
      sandbox.stub(SurveyRepository, 'findSurveys').resolves([]);
      const params = {
        data: { surveyId: new ObjectId(), aggregatedSurveyIds: [] },
        initiative: initiativeOneSimple,
        user: userOne,
      };
      await expect(surveyAggregator.updateAggregatedSurveyConfig(params)).to.be.eventually.rejectedWith(
        UserError,
        /You must select at least one survey/
      );
    });

    it('should call functions to update & refresh data', async () => {
      const surveyIdToUpdate = new ObjectId();
      const survey = new Survey({
        _id: surveyIdToUpdate,
        initiativeId: initiativeOneSimple._id,
        sourceName: Blueprints.Gri2020,
        code: 'combined-survey',
      });
      const surveyTwo = { ...surveyOne, _id: new ObjectId() };
      sandbox.stub(survey, 'save').resolves(survey);
      const surveysToAggregate = [surveyOne, surveyTwo];
      sandbox.stub(SurveyRepository, 'findSurveys').resolves(surveysToAggregate);
      sandbox.stub(SurveyRepository, 'mustFindById').resolves(survey);
      sandbox.stub(surveyComposer, 'composeBlueprint').resolves(gri2020);
      const updateSurveyDataStub = sandbox.stub(surveyAggregator, 'updateAggregatedSurvey');

      const params = {
        data: {
          surveyId: surveyIdToUpdate,
          aggregatedSurveyIds: [surveyOne._id, surveyTwo._id],
          scope: SurveyScope.createEmpty(),
          name: 'Updated combined survey',
          period: DataPeriods.Monthly,
        },
        initiative: initiativeOneSimple,
        user: userOne,
      };

      await surveyAggregator.updateAggregatedSurveyConfig(params);
      expect(updateSurveyDataStub.calledOnce).to.be.true;
      const updatedSurvey = updateSurveyDataStub.args[0][0].toObject();
      expect(updatedSurvey.sourceItems).to.deep.eq([surveyOne._id, surveyTwo._id].map((id) => ({ sourceId: id, type: SourceItemType.Survey })));
      expect(updatedSurvey.scope).to.deep.eq(params.data.scope);
      expect(updatedSurvey.name).to.eq(params.data.name);
      expect(updatedSurvey.period).to.eq(params.data.period);
    });
  });

  describe('createAggregatedSurveyWithValidation', () => {
    afterEach(() => sandbox.restore());

    const baseOptions = {
      name: 'Test Aggregated Survey',
      company: initiativeOneSimple,
      type: SurveyType.Aggregation as const,
      effectiveDate: '2023-01-01',
      sourceName: Blueprints.Gri2020,
      utrvType: UtrvType.Actual,
      user: userOne,
      surveyIdsToAggregate: [new ObjectId(), new ObjectId()],
    };

    describe('Successful aggregation', () => {
      it('should successfully create aggregated survey when surveys have matching currencies', async () => {
        const surveysToAggregate = [
          {
            _id: new ObjectId(),
            initiativeId: initiativeOneSimpleId,
            effectiveDate: new Date('2023-01-01'),
            scope: SurveyScope.createEmpty(),
            period: DataPeriods.Yearly,
            unitConfig: { ...blueprintDefaultUnitConfig, currency: 'EUR' },
          },
          {
            _id: new ObjectId(),
            initiativeId: initiativeOneSimpleId,
            effectiveDate: new Date('2023-01-01'),
            scope: SurveyScope.createEmpty(),
            period: DataPeriods.Yearly,
            unitConfig: { ...blueprintDefaultUnitConfig, currency: 'EUR' },
          },
        ];

        const getSurveysStub = sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysToAggregate);
        const createAggregatedStub = sandbox.stub(surveyAggregator, 'createAggregatedSurvey');

        await surveyAggregator.createAggregatedSurveyWithValidation(baseOptions);

        expect(getSurveysStub.calledOnce).to.be.true;
        expect(getSurveysStub.calledWith(baseOptions.surveyIdsToAggregate)).to.be.true;
        expect(createAggregatedStub.calledOnce).to.be.true;
        expect(createAggregatedStub.calledWith({
          surveysToAggregate,
          name: baseOptions.name,
          company: baseOptions.company,
          type: baseOptions.type,
          effectiveDate: baseOptions.effectiveDate,
          sourceName: baseOptions.sourceName,
          utrvType: baseOptions.utrvType,
          user: baseOptions.user,
          currency: 'EUR',
        })).to.be.true;
      });

      it('should work with single survey aggregation', async () => {
        const singleSurvey = [{
          _id: new ObjectId(),
          initiativeId: initiativeOneSimpleId,
          effectiveDate: new Date('2023-01-01'),
          scope: SurveyScope.createEmpty(),
          period: DataPeriods.Yearly,
          unitConfig: { ...blueprintDefaultUnitConfig, currency: 'EUR' },
        }];


        sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(singleSurvey);
        const createAggregatedStub = sandbox.stub(surveyAggregator, 'createAggregatedSurvey');

        await surveyAggregator.createAggregatedSurveyWithValidation({
          ...baseOptions,
          surveyIdsToAggregate: [singleSurvey[0]._id],
        });

        expect(createAggregatedStub.calledOnce).to.be.true;
        expect(createAggregatedStub.calledWith({
          surveysToAggregate: singleSurvey,
          name: baseOptions.name,
          company: baseOptions.company,
          type: baseOptions.type,
          effectiveDate: baseOptions.effectiveDate,
          sourceName: baseOptions.sourceName,
          utrvType: baseOptions.utrvType,
          user: baseOptions.user,
          currency: 'EUR',
        })).to.be.true;
      });
    });

    describe('Currency mismatch', () => {
      it('should throw UserError when surveys have different currencies', async () => {
        const surveysWithDifferentCurrencies = [
          {
            _id: new ObjectId(),
            initiativeId: initiativeOneSimpleId,
            effectiveDate: new Date('2023-01-01'),
            scope: SurveyScope.createEmpty(),
            period: DataPeriods.Yearly,
            unitConfig: { ...blueprintDefaultUnitConfig, currency: 'USD' },
          },
          {
            _id: new ObjectId(),
            initiativeId: initiativeOneSimpleId,
            effectiveDate: new Date('2023-01-01'),
            scope: SurveyScope.createEmpty(),
            period: DataPeriods.Yearly,
            unitConfig: { ...blueprintDefaultUnitConfig, currency: 'EUR' },
          },
        ];

        sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysWithDifferentCurrencies);
        const createAggregatedStub = sandbox.stub(surveyAggregator, 'createAggregatedSurvey');

        await expect(surveyAggregator.createAggregatedSurveyWithValidation(baseOptions))
          .to.be.rejectedWith(UserError, 'Cannot aggregate surveys with different currencies');

        expect(createAggregatedStub.called).to.be.false;
      });
    });

    describe('Edge cases', () => {
      it('should handle invalid survey IDs that return no surveys', async () => {
        const invalidSurveyIds = [new ObjectId(), new ObjectId()];
        sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves([]);
        const createAggregatedStub = sandbox.stub(surveyAggregator, 'createAggregatedSurvey');

        await expect(surveyAggregator.createAggregatedSurveyWithValidation({
          ...baseOptions,
          surveyIdsToAggregate: invalidSurveyIds,
        })).to.be.rejectedWith(UserError, 'Cannot aggregate surveys. No surveys found.');

        expect(createAggregatedStub.called).to.be.false;
      });
    });
  });
});
