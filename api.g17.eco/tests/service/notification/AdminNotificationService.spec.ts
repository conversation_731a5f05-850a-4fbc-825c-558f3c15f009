import { expect } from 'chai';
import sinon from 'sinon';
import { ObjectId } from 'bson';
import type { PipelineStage } from 'mongoose';
import { createAggregate } from '../../setup';
import { getFirstArg } from '../../utils/sinon-helpers';
import { AdminNotificationService } from '../../../server/service/notification/AdminNotificationService';
import Notification from '../../../server/models/notification';
import NotificationSchedule from '../../../server/models/notification-schedule';
import UserDailyNotification from '../../../server/models/userDailyNotification';
import ScheduledNotification from '../../../server/models/scheduledNotification';

describe('AdminNotificationService', () => {
  const sandbox = sinon.createSandbox();
  let service: AdminNotificationService;

  beforeEach(() => {
    service = new AdminNotificationService();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getSystemNotifications', () => {
    it('should return system notifications with default filters', async () => {
      const mockNotifications = [
        {
          _id: new ObjectId(),
          title: 'Test Notification',
          category: 'test',
          created: new Date(),
          recipients: [{ userId: new ObjectId(), status: 'sent' }],
        },
      ];

      sandbox.stub(Notification, 'aggregate').returns(createAggregate(mockNotifications));

      const result = await service.getSystemNotifications({
        limit: 50,
        skip: 0,
      });

      expect(result).to.have.property('data');
      expect(result).to.have.property('stats');
      expect(result.data).to.deep.equal(mockNotifications);
    });

    it('should filter by userId when provided', async () => {
      const userId = new ObjectId().toString();
      const aggregateStub = sandbox.stub(Notification, 'aggregate').returns(createAggregate([]));

      await service.getSystemNotifications({
        userId,
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const matchStage = pipeline.find((stage): stage is PipelineStage.Match => '$match' in stage);

      expect(matchStage).to.exist;
      expect(matchStage?.$match).to.have.property('recipients.userId');
    });

    it('should filter by date range when provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');
      const aggregateStub = sandbox.stub(Notification, 'aggregate').returns(createAggregate([]));

      await service.getSystemNotifications({
        startDate,
        endDate,
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const matchStage = pipeline.find((stage): stage is PipelineStage.Match => '$match' in stage);

      expect(matchStage).to.exist;
      const created = matchStage?.$match?.created as Record<string, Date>;
      expect(created).to.have.property('$gte');
      expect(created).to.have.property('$lte');
    });

    it('should apply search filter when searchStr is provided', async () => {
      const aggregateStub = sandbox.stub(Notification, 'aggregate').returns(createAggregate([]));

      await service.getSystemNotifications({
        searchStr: 'test search',
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const searchStage = pipeline.find((stage): stage is PipelineStage.Match =>
        '$match' in stage && 'title' in (stage.$match || {})
      );

      expect(searchStage).to.exist;
      const titleFilter = searchStage?.$match?.title as Record<string, unknown>;
      expect(titleFilter).to.deep.equal({ $regex: 'test search', $options: 'i' });
    });
  });

  describe('getUserDailyNotifications', () => {
    it('should return daily notifications with stats', async () => {
      const mockNotifications = [
        {
          _id: new ObjectId(),
          userId: new ObjectId(),
          type: 'survey_deadline',
          scheduledDate: new Date(),
          completedDate: new Date(),
        },
      ];
      const mockStats = { total: 1, pending: 0, completed: 1, errored: 0, skipped: 0 };

      const aggregateStub = sandbox.stub(UserDailyNotification, 'aggregate');
      aggregateStub.onFirstCall().returns(createAggregate(mockNotifications));
      aggregateStub.onSecondCall().returns(createAggregate([mockStats]));

      const result = await service.getUserDailyNotifications({
        limit: 50,
        skip: 0,
      });

      expect(result).to.have.property('data');
      expect(result).to.have.property('stats');
      expect(result.data).to.deep.equal(mockNotifications);
      expect(result.stats).to.deep.equal(mockStats);
    });

    it('should filter by status when provided', async () => {
      const aggregateStub = sandbox.stub(UserDailyNotification, 'aggregate').returns(
        createAggregate([])
      );

      await service.getUserDailyNotifications({
        status: 'pending',
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const matchStage = pipeline.find((stage): stage is PipelineStage.Match => '$match' in stage);

      expect(matchStage).to.exist;
      expect(matchStage?.$match).to.have.property('completedDate');
      expect(matchStage?.$match).to.have.property('erroredDate');
      expect(matchStage?.$match).to.have.property('skippedDate');
    });

    it('should filter by userId when provided', async () => {
      const userId = new ObjectId().toString();
      const aggregateStub = sandbox.stub(UserDailyNotification, 'aggregate').returns(
        createAggregate([])
      );

      await service.getUserDailyNotifications({
        userId,
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const matchStage = pipeline.find((stage): stage is PipelineStage.Match => '$match' in stage);

      expect(matchStage).to.exist;
      expect(matchStage?.$match).to.have.property('userId');
    });
  });

  describe('getScheduledNotifications', () => {
    it('should return scheduled notifications with initiative data', async () => {
      const mockNotifications = [
        {
          _id: new ObjectId(),
          type: 'survey_deadline',
          scheduledDate: new Date(),
          initiativeId: new ObjectId(),
          initiative: { name: 'Test Initiative', code: 'TEST' },
        },
      ];

      sandbox.stub(ScheduledNotification, 'aggregate').returns(
        createAggregate(mockNotifications)
      );

      // @ts-expect-error - Stubbing countDocuments for testing
      sandbox.stub(ScheduledNotification, 'countDocuments').returns({
        exec: sandbox.stub().resolves(1)
      });

      const result = await service.getScheduledNotifications({
        limit: 50,
        skip: 0,
      });

      expect(result).to.have.property('data');
      expect(result).to.have.property('stats');
      expect(result.data).to.deep.equal(mockNotifications);
    });

    it('should filter by status when provided', async () => {
      const aggregateStub = sandbox.stub(ScheduledNotification, 'aggregate').returns(
        createAggregate([])
      );

      // @ts-expect-error - Stubbing countDocuments for testing
      sandbox.stub(ScheduledNotification, 'countDocuments').returns({
        exec: sandbox.stub().resolves(0)
      });

      await service.getScheduledNotifications({
        status: 'completed',
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const matchStage = pipeline.find((stage): stage is PipelineStage.Match => '$match' in stage);

      expect(matchStage).to.exist;
      expect(matchStage?.$match).to.have.property('completedDate');
    });
  });

  describe('getNotificationSchedules', () => {
    it('should return notification schedules with user and initiative data', async () => {
      const mockSchedules = [
        {
          _id: new ObjectId(),
          userId: new ObjectId(),
          initiativeId: new ObjectId(),
          notificationIds: [new ObjectId()],
          userEmail: '<EMAIL>',
          userName: 'Test User',
          initiative: { name: 'Test Initiative', code: 'TEST' },
        },
      ];

      sandbox.stub(NotificationSchedule, 'aggregate').returns(
        createAggregate(mockSchedules)
      );

      const result = await service.getNotificationSchedules({
        limit: 50,
        skip: 0,
      });

      expect(result).to.have.property('data');
      expect(result).to.have.property('stats');
      expect(result.data).to.deep.equal(mockSchedules);
    });

    it('should filter by userId when provided', async () => {
      const userId = new ObjectId().toString();
      const aggregateStub = sandbox.stub(NotificationSchedule, 'aggregate').returns(
        createAggregate([])
      );

      await service.getNotificationSchedules({
        userId,
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      const matchStage = pipeline.find((stage): stage is PipelineStage.Match => '$match' in stage);

      expect(matchStage).to.exist;
      expect(matchStage?.$match).to.have.property('userId');
    });

    it('should apply search filter when searchStr is provided', async () => {
      const aggregateStub = sandbox.stub(NotificationSchedule, 'aggregate').returns(
        createAggregate([])
      );

      await service.getNotificationSchedules({
        searchStr: '<EMAIL>',
        limit: 50,
        skip: 0,
      });

      const pipeline = getFirstArg<PipelineStage[]>(aggregateStub) || [];
      
      // Find the search stage after the $unwind
      const searchStage = pipeline.find((stage, index) => {
        // Look for the search filter after $unwind stage
        if (index > 0 && '$match' in stage) {
          const matchStage = stage as PipelineStage.Match;
          return matchStage.$match?.$or !== undefined;
        }
        return false;
      });

      expect(searchStage).to.exist;
      const orConditions = (searchStage as PipelineStage.Match)?.$match?.$or as Array<Record<string, unknown>>;
      expect(orConditions).to.have.lengthOf(3); // email, firstName, surname
    });
  });


});
