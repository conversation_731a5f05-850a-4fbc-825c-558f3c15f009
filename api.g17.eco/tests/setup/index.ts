/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as chai from 'chai';
import type { SinonSandbox, SinonStub } from 'sinon';
import { ObjectId } from 'bson';
import { config } from 'dotenv';
import chaiAsPromised from 'chai-as-promised';
import type { Aggregate, Query } from "mongoose";
import ContextError from '../../server/error/ContextError';

config({path: './.env.test'});
chai.use(chaiAsPromised);

export const returnData = function (result: any, lean: boolean) {
  const save = lean ? undefined : function (this: any) {
    return this;
  };

  return Array.isArray(result)
    ? result.map((r) => ({ _id: new ObjectId(), save, ...r }))
    : { _id: new ObjectId(), save, ...result };
};

type callsFakeFn = (...args: any[]) => any;

export const createMongooseModel = function <T = any>(toReturn: T, failError: Error = new ContextError('Document not found')) {
  const fake: { [key: string]: () => any } = {};
  fake.exec = () => (toReturn);
  fake.lean = () => fake;
  fake.countDocuments = () => fake;
  fake.orFail = () => {
    fake.exec = () => {
      if (!toReturn) {
        // eslint-disable-next-line no-restricted-syntax
        throw failError;
      }
      return toReturn;
    };
    return fake;
  };
  fake.populate = () => fake;
  fake.sort = () => fake;
  fake.limit = () => fake;
  fake.skip = () => fake;
  return fake as unknown as Query<T, any>;
};

export const createAggregate = function<T = any> (toReturn: T) {
  const fake: { [key: string]: () => any } = {};
  fake.exec = async () => (toReturn);
  // oxlint-disable-next-line no-thenable
  fake.then = async () => (toReturn);
  fake.sort = () => fake;
  return fake as unknown as Aggregate<T>;
};

export const wrapStub = <T>(
  sandbox: SinonSandbox,
  model: T,
  method: keyof T,
  result: object[] | object | callsFakeFn,
  lean: boolean = true,
): SinonStub => {

  const stub = sandbox.stub(model, method);

  if (typeof result === 'object') {
    const toReturn = returnData(result, lean);
    stub.callsFake(() => {

      // Model Wrapper
      return createMongooseModel(toReturn);
    });
    return stub;
  }

  stub.callsFake(result);
  return stub;
};

export default {
  wrapStub,
};
