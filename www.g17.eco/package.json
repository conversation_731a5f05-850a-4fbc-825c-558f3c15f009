{"name": "wwg", "version": "0.1.0", "private": true, "dependencies": {"@analytics/google-analytics": "^1.0.7", "@analytics/google-tag-manager": "^0.5.5", "@analytics/segment": "^1.1.4", "@apollo/client": "^3.7.3", "@fontsource/inter": "^4.5.14", "@fortawesome/fontawesome-pro": "^6.5.1", "@g17eco/convert-units": "^2.14.0", "@g17eco/core": "^3.9.0", "@hocuspocus/provider": "^2.15.0", "@lexical/react": "^0.29.0", "@magicbell/magicbell-react": "^10.9.11", "@okta/okta-auth-js": "^7.12.1", "@okta/okta-react": "^6.10.0", "@reduxjs/toolkit": "^2.6.1", "@sentry/react": "^10.5.0", "@sentry/vite-plugin": "^2.19.0", "@table-library/react-table-library": "^4.1.7", "@tanstack/react-table": "^8.19.2", "@types/react-grid-layout": "^1.3.2", "analytics": "^0.8.1", "axios": "^1.8.4", "bootstrap": "^5.2.3", "browserslist": "^4.21.5", "canvas-confetti": "^1.9.3", "classnames": "^2.3.2", "d3": "^6.7.0", "d3-flextree": "^2.1.2", "d3-v6-tip": "^1.0.9", "dayjs": "^1.11.7", "detect-browser": "^5.3.0", "docx": "^7.8.2", "dotenv": "^16.1.4", "env-cmd": "^10.1.0", "exifreader": "^4.21.0", "file-saver": "^2.0.2", "flexsearch": "^0.7.21", "flickity": "^2.3.0", "fuse.js": "^6.6.2", "graphql": "^16.6.0", "highlight-words-core": "^1.2.2", "html-to-image": "^1.11.4", "html2canvas": "^1.4.1", "imagesloaded": "^5.0.0", "js-file-download": "^0.4.12", "jsonata": "^2.0.4", "jspdf": "^2.5.2", "lexical-beautiful-mentions": "^0.1.44", "mathjs": "^13.0.3", "papaparse": "^5.3.2", "prop-types": "^15.8.1", "query-string": "^8.1.0", "react": "^18.3.1", "react-autosuggest": "^10.1.0", "react-circular-progressbar": "^2.1.0", "react-clamp-lines": "^3.0.3", "react-cookie-consent": "^8.0.1", "react-date-range": "^1.4.0", "react-dom": "^18.3.1", "react-draggable": "^4.4.5", "react-dropdown-tree-select": "^2.8.0", "react-dropzone": "^14.2.3", "react-google-charts": "^3.0.15", "react-grid-layout": "^1.3.4", "react-helmet": "^6.1.0", "react-hot-toast": "^2.4.1", "react-hotjar": "^5.4.1", "react-in-viewport": "^1.0.0-alpha.29", "react-mentions": "^4.4.7", "react-move": "^6.5.0", "react-redux": "^9.2.0", "react-router-dom": "^5.3.4", "react-router-dom-v5-compat": "^6.19.0", "react-scrollspy": "^3.4.2", "react-select": "^5.7.0", "react-slick": "^0.29.0", "react-window": "^1.8.7", "reactstrap": "^9.2.3", "sanitize-filename": "^1.6.3", "semver-compare": "^1.0.0", "slick-carousel": "^1.8.1", "strip-ansi": "^7.1.0", "topojson": "^3.0.2", "use-debounce": "^9.0.3", "xlsx": "^0.18.5", "yaml": "^2.8.0", "yet-another-react-lightbox": "^3.15.6", "yjs": "^13.6.20", "zxcvbn": "^4.4.2"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@eslint/compat": "^1.2.7", "@eslint/js": "^9.21.0", "@faker-js/faker": "^8.4.1", "@lexical/eslint-plugin": "^0.29.0", "@playwright/experimental-ct-react": "^1.52.0", "@playwright/test": "1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/canvas-confetti": "^1.6.0", "@types/classnames": "^2.3.1", "@types/d3": "^7.4.0", "@types/d3-flextree": "^2.1.1", "@types/file-saver": "^2.0.5", "@types/flexsearch": "^0.7.3", "@types/flickity": "^2.2.7", "@types/highlight-words-core": "^1.2.1", "@types/mathjs": "^9.4.2", "@types/node": "^22.14.1", "@types/papaparse": "^5.3.7", "@types/react": "^18.3.16", "@types/react-autosuggest": "^10.1.5", "@types/react-color": "^3.0.6", "@types/react-date-range": "^1.4.4", "@types/react-dom": "^18.3.5", "@types/react-helmet": "^6.1.7", "@types/react-mentions": "^4.1.8", "@types/react-router-dom": "^5.3.3", "@types/react-scrollspy": "^3.3.5", "@types/react-slick": "^0.23.10", "@types/react-window": "^1.8.8", "@types/topojson": "^3.2.3", "@types/zxcvbn": "^4.4.1", "@vitejs/plugin-react-swc": "^3.7.1", "@vitest/browser": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.2", "babel-plugin-named-exports-order": "^0.0.2", "eslint": "^9.21.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-oxlint": "^0.15.12", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testing-library": "^7.1.1", "globals": "^16.0.0", "jsdom": "^22.1.0", "msw": "^2.3.5", "oxlint": "^0.15.13", "playwright": "1.52.0", "prettier": "^3.4.2", "prettier-plugin-organize-attributes": "^1.0.0", "react-select-event": "^5.5.1", "sass": "^1.57.1", "ts-node": "^10.9.1", "typescript": "^5.5.4", "typescript-eslint": "^8.25.0", "vite": "^5.4.10", "vite-plugin-checker": "^0.9.0", "vite-plugin-ejs": "^1.7.0", "vite-plugin-oxlint": "^1.1.3", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.2", "vitest": "^3.1.2", "vitest-browser-react": "^0.1.1"}, "overrides": {"vite-plugin-oxlint": {"oxlint": "$oxlint"}}, "engines": {"node": ">=22.14.0"}, "scripts": {"preinstall": "npx google-artifactregistry-auth --repo-config=$HOME/.npmrc", "artifactregistry-login": "npx google-artifactregistry-auth --repo-config=$HOME/.npmrc", "gcp-login": "npx google-artifactregistry-auth --repo-config=$HOME/.npmrc", "start": "vite", "serve": "vite preview", "dev": "vite", "dev:test": "vite --mode test", "lint": "oxlint --import-plugin --tsconfig=./tsconfig.json src && eslint src", "generate:firebase": "TS_NODE_SKIP_PROJECT=1 vite-node deploy/generateFirebase.ts", "build": "vite build --mode development", "check-types": "tsc --noEmit", "test": "vitest watch", "test:update-snapshot": "vitest -u", "test:coverage": "vitest run --coverage.enabled=true --coverage.reporter=json --outputFile=coverage/coverage-final.json", "test:coverage:ai": "vitest run --coverage.enabled=true --coverage.reporter=text-summary --coverage.reporter=text", "test:ci": "vitest run", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:integration": "playwright test --project=integration", "test:smoke": "playwright test --project=smoke", "test-ct": "playwright test -c playwright-ct.config.ts", "test:components": "vitest --run --project=browser", "analyze": "source-map-explorer 'build/static/js/*.js'", "build:nightly": "vite build --mode nightly", "build:nightly2": "vite build --mode nightly2", "build:staging": "vite build --mode staging", "build:staging-singapore": "vite build --mode staging-singapore", "build:loadtest": "vite build --mode loadtest", "build:demo": "vite build --mode demo", "build:demo-k8s": "vite build --mode demo-k8s", "build:prod-uae": "vite build --mode uae", "build:prod-ksa": "vite build --mode ksa", "build:prod-singapore": "vite build --mode singapore", "build:prod-singapore-k8s": "vite build --mode singapore-k8s", "build:prod-london": "vite build --mode prod", "build:prod-london-k8s": "vite build --mode prod-k8s", "deploy:uae": "swa deploy --app-name g17eco-www --env Production"}, "type": "module", "prettier": {"singleQuote": true, "tsxSingleQuote": true, "jsxSingleQuote": true}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "exifreader": {"include": {"exif": ["DateTime", "GPSLatitude", "GPSLatitudeRef", "GPSLongitude", "GPSLongitudeRef", "GPSAltitude", "GPSAltitudeRef"]}}}