# Gemini Code-gen Guidelines

This document provides guidance to <PERSON> when working with code in this repository. It is based on the existing `CLAUDE.md` file.

## Project Overview

This is a React-based sustainability reporting and tracking platform built with TypeScript and Vite.

## Key Commands

*   **Start Development Server:** `npm start`
*   **Run Tests (Watch Mode):** `npm test`
*   **Run Tests (Single Run):** `npm run test:ci`
*   **Run E2E Tests:** `npm run test:e2e`
*   **Lint Code:** `npm run lint`
*   **Check Types:** `npm run check-types`

## Architecture

### Folder Structure (Atomic & Modular)

This project uses a hybrid of Atomic Design and a modular app architecture.

*   **`/src/atoms/`**: Basic, reusable UI components (e.g., `Button`, `Input`).
*   **`/src/molecules/`**: Combinations of atoms.
*   **`/src/components/`**: (LEGACY) More complex components with business logic. **Do not add new components here.**
*   **`/src/features/`**: Feature-specific components and logic.
*   **`/src/apps/`**: Self-contained feature modules (e.g., `carbon-calculator`, `portfolio-tracker`). **Crucially, apps must not import from each other.**

### State Management

*   **Redux Toolkit (RTK) is the standard.**
*   **RTK Query:** Use for all new API interactions. Define API slices in `/src/api/`.
*   **RTK Slices:** For new client-side UI state, use the slice structure found in `/src/slice/`.
*   Legacy Redux patterns (`/src/reducers/`, `/src/actions/`) exist but should be avoided for new features.

### API Layer

*   **REST APIs:** Use the Axios-based clients in `/src/api/` integrated with RTK Query.
*   **CMS Content:** Use Apollo Client for GraphQL to fetch content from Hygraph CMS.
*   **Authentication:** Handled via Okta.
*   **Configuration:** All API and environment-specific settings are in `/src/config.ts` and sourced from `.env.*` files.

### Routing

*   **React Router v5** (with a v6 compatibility layer).
*   Route definitions are in `/src/routes/`.

## Development Patterns

### Creating New Components

1.  **Determine the correct atomic level** (atom, molecule, component, or feature).
2.  Place the file in the corresponding directory.
3.  Use **TypeScript** for props and internal types. Avoid `any`.
4.  Add a co-located unit test file (e.g., `MyComponent.spec.tsx`).

### Integrating New APIs

1.  Add the API definition to the relevant RTK Query API slice in `/src/api/`.
2.  Define TypeScript types for the request and response.
3.  Use the auto-generated RTK Query hooks in your components.
4.  Add mock handlers in MSW for testing purposes.

### Import Restrictions

*   ESLint enforces strict architectural boundaries. Pay attention to linting errors.
*   The dependency flow is: `atoms` -> `molecules` -> `components` -> `features`.
*   **Apps cannot import from other apps.**

## Testing

*   **Unit Tests:** Use Vitest and React Testing Library. Files are co-located with the component.
*   **E2E Tests:** Use Playwright. Tests are in the `/integration-test/` directory.
*   **Integration Tests:** Use Cucumber. Features are in `/integration-test/features/`.
*   **Focused Testing:** To save time and resources, run tests specifically for the file you are editing, rather than the entire test suite.
*   **Use `npm run test:ci` for running tests in a non-interactive way.**

**CRITICAL**: When writing or modifying tests, you MUST follow the testing guidelines in `.agent-docs/testing-guidelines-complete.md`. Key requirements:
*   Always use `userEvent.setup()` pattern, never direct userEvent import
*   Never use `fireEvent`, always use user-event
*   Use `vi` for mocking, not `jest`
*   Follow async patterns with proper awaiting
*   Use the `createTestSetup()` helper for MSW server setup
*   See `.agent-docs/testing-guidelines-complete.md` for complete patterns and examples

## Common Gotchas

*   **Permissions:** The application has a complex, multi-tenant permission system. Always check user roles and workgroups before assuming access.
*   **CSP:** The Content Security Policy is strict in production. Be mindful of external resources.