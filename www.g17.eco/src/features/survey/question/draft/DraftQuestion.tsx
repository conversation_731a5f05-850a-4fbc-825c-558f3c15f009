/*
 * Copyright (c) 2019-2025. World Wide Generation Ltd
 */

import '../Question.scss';
import { RefObject } from 'react';
import { getNaType, getValueDataProp, isAssuredLocked, isNa, isNr } from '@utils/universalTrackerValue';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { NotApplicableTypes } from '@constants/status';
import { getDefaultPlaceholder } from '@features/question-input/QuestionInput';
import { BaseInputProps } from '@features/survey/form/input/InputProps';
import InputFactory from '@features/survey/form/input/InputFactory';
import { QuestionInformation } from '@features/survey/question/QuestionInformation';
import { useProviderFactory } from '@features/rich-text-editor';
import { UserMin } from '@g17eco/types/user';
import { SurveyModelMinimalUtrv } from '@g17eco/types/survey';
import UniversalTrackerClassInterface from '../../../../model/UniversalTrackerClassInterface';
import { QuestionProps } from '../../../../types/questionInterfaces';

interface Props extends QuestionProps {
  currentUser: UserMin | undefined;
  containerRef?: RefObject<HTMLDivElement>;
}

const noop = () => {
};

/**
 * Work in progress version of question view using draft mode
 * implemented through websocket provider.
 *
 * @TODO: WIP and not yet fully implemented.
 * Experimental for internal use only in development/testing environments
 */
export const DraftQuestion = (props: Props) => {

  const {
    utr,
    utrv,
    saving,
    survey,
    alternativeCodeOverride: alternativeCode,
    isAggregate,
    isContributor,
    isQuestionReadOnly = false,
    index,
    value,
    updateTable,
    table,
    valueData,
    displayCheckbox,
    unit,
    numberScale,
    scrollToRef,
    update,
    handleNA,
    handleNR,
    handleReject,
    handleComments,
    hasValueChanged,
    addons,
    currentUser,
  } = props;


  const { providerFactory } = useProviderFactory();
  const isSurveyComplete = Boolean(survey?.completedDate);

  const renderValueInputContainer = (utr: UniversalTrackerClassInterface, utrv: SurveyModelMinimalUtrv, isReadOnly: boolean) => {
    const valueDataData = getValueDataProp(valueData, utr.getValueType());
    const defaultPlaceholder = getDefaultPlaceholder(utr);

    // Original utrv is in NA state, but we enter a valid we allow questionValue to change
    const questionValue = getNaType({
      // if we set a value this should not be undefined
      value,
      // still need to read the original valueData, not potential valueData.input
      // that will not contain notApplicableType
      valueData: utrv.valueData,
      status: utrv.status,
    }) === NotApplicableTypes.na ? undefined : value;


    const data: BaseInputProps = {
      isDraft: true,
      currentUser,
      providerFactory,
      documentId: `utrv.${utrv._id}`,

      prefix: undefined,
      suffix: undefined,
      universalTracker: utr,
      unitConfig: survey.unitConfig,
      isDisabled: () => isReadOnly,
      handleFocus: noop,

      handleError: noop,
      handleCheckboxChange: noop,
      handleValueDataChange: () => undefined,
      handleValueChange: noop,
      handleUnitChange: (unit) => update({ unit }),
      handleNumberScaleChange: (numberScale) => update({ numberScale }),
      handleNA,
      handleNR,
      handleReject,
      handleComments,

      // Input values
      valueDataData: valueDataData,
      questionValue,

      // Multi-row table
      table,
      updateTable,

      placeholder: isNr(utrv) ? 'N/R' : isNa(utrv) ? 'N/A' : defaultPlaceholder,
      saving,
      index,
      unit,
      numberScale,
      displayCheckbox,
      scrollToRef,
      hasValueChanged,
      addons,
      isDownloadBtnBottom: true,
      status: utrv.status,
      initiativeUtr: props.initiativeUtr,
      users: props.users,
      isPrivate: utrv.isPrivate,
    };

    return InputFactory(data);
  }

  const isLocked = (utrv && isAssuredLocked(utrv)) || false;
  const isReadOnly = isSurveyComplete || saving || !isContributor || isAggregate || isLocked || isQuestionReadOnly;

  return (
    <div className='surveyQuestion'>
      <QuestionInformation classes={{ container: 'mt-3' }} utr={utr} alternativeCode={alternativeCode} />
      <div className='mt-4 position-relative'>
        <LoadingPlaceholder height={36} className='mb-3' isLoading={saving || !utr || !utrv}>
          {!(saving || !utr || !utrv) ? renderValueInputContainer(
            utr,
            utrv,
            isReadOnly,
          ) : null}
        </LoadingPlaceholder>
      </div>
    </div>
  );
}
