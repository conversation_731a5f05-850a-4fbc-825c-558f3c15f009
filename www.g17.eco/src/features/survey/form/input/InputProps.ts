/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Addons, DisplayCheckBox, ErrorMessageType, InputColumn, TableDataInfo, ValueDataDataWithTable } from '@g17eco/types/questionInterfaces';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { ProviderFactory } from '@g17eco/types/collaboration';
import { TableColumn, ValueTable } from '@g17eco/types/universalTracker';
import { ValueData, ValueDataSourceConnection } from '@g17eco/types/universalTrackerValue';
import { UserMin } from '@g17eco/types/user';
import { UnitConfig } from '@g17eco/types/survey';
import UniversalTrackerClassInterface from '../../../../model/UniversalTrackerClassInterface';

export type HandleValueChangeProps = {
  value: any;
  min?: number;
  max?: number;
  isReadOnly?: boolean;
  source?: ValueDataSourceConnection;
};

export type HandleValueChange = ({
  value,
  min,
  max,
  isReadOnly,
  source,
}: HandleValueChangeProps) => void;

export interface BaseInputProps {
  prefix: any;
  suffix: any;
  index: number;
  saving: boolean;
  universalTracker: UniversalTrackerClassInterface;
  initiativeUtr?: InitiativeUniversalTracker;
  status: string;
  placeholder?: string;
  displayCheckbox: DisplayCheckBox;
  scrollToRef: () => void;
  inputMessage?: ErrorMessageType;
  isDisabled?: (props?: Partial<BaseInputProps> | Partial<ColumnInputProps>) => boolean;
  isInvalid?: boolean;
  unitConfig?: UnitConfig;

  // Draft
  isDraft?: boolean;
  providerFactory?: ProviderFactory;
  currentUser?: UserMin;
  documentId?: string;
  /** Represent users for comments **/
  users?: UserMin[];

  // Input
  questionValue: number | undefined;
  valueDataData: ValueDataDataWithTable;
  unit: string | undefined;
  numberScale: string | undefined;

  // Multi-row table
  table: TableDataInfo;
  updateTable: (table: Partial<TableDataInfo>, error?: ErrorMessageType) => any;
  isDownloadBtnBottom?: boolean;

  // Handlers
  handleFocus?: () => any;
  handleValueDataChange: (valueDataData: any, source?: ValueDataSourceConnection) => any;
  handleCheckboxChange: (inputName: string, value: any) => any;
  handleError: (message: string) => any;
  handleValueChange: HandleValueChange;
  handleUnitChange: (unit: string) => any;
  handleNumberScaleChange: (numberScale: string) => any;

  handleNA?: () => void;
  handleNR?: () => void;
  handleReject?: () => void;
  handleComments?: (comments: string) => void;

  ref?: any;
  hasValueChanged?: boolean;

  // A single input (Date, Text, Number, ValueList) will take the first element for addon
  // Multiple inputs (Numeric/Text value list) an array of addons is used
  // A single row table uses an array located by rowIndex = 0
  // A multiple rows table uses an array located by rowIndex and code (columnCode)
  addons?: Addons;
  isReadOnlyOptions?: boolean;
  // Private used to disable AI draft for text inputs
  isPrivate?: boolean;
}

export interface InputProps extends BaseInputProps {
  valueDataData: ValueData['data'];
}

export type TableValueDataData = InputColumn[][];
export interface ColumnInputProps extends BaseInputProps {
  column: TableColumn;
  inputColumn: InputColumn;
  placeholder?: string,
  label?: string | JSX.Element;
  isAutoCalculated: boolean;
  valueDataData: TableValueDataData;
  multi?: boolean;
  updateColumn: (update: InputColumn) => void;
}

export interface TableInputProps extends BaseInputProps {
  tableConfiguration: ValueTable;
  calculationColumns: TableColumn[];
  visibilityRuleColumns: TableColumn[];
  placeholder?: string,
  valueDataData: TableValueDataData;
  handleValueDataChange: (update: InputColumn[][]) => void;
  handleCheckboxChange: (checkboxId: string, checked: boolean) => void;
  scrollToRef: () => void,
  ref?: any,

  handleNA?: () => void;
  handleNR?: () => void;
  handleReject?: () => void;
  handleComments?: (comments: string) => void;
}
