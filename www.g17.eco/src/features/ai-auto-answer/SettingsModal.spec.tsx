import { render, screen } from '@testing-library/react';
import { SettingsModal } from './SettingsModal';
import { useGetAIDocumentScanQuery } from '@api/ai';
import { AI_MESSAGE } from '@constants/ai';
import { Mock } from 'vitest';
import { BrowserRouter } from 'react-router-dom';

vi.mock(import('@api/ai'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useGetAIDocumentScanQuery: vi.fn(),
  };
});

describe('SettingsModal', () => {
  const defaultProps = {
    isOpen: true,
    toggle: vi.fn(),
    initiativeId: 'test-initiative-id',
    handleSubmit: vi.fn(),
    canAccessAIDocumentLibrary: true,
  };

  const renderWithRouter = (component: React.ReactElement) => {
    return render(<BrowserRouter>{component}</BrowserRouter>);
  };

  beforeEach(() => {
    (useGetAIDocumentScanQuery as Mock).mockReturnValue({
      data: null,
      isSuccess: false,
      error: null,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should render the modal when isOpen is true', () => {
    renderWithRouter(<SettingsModal {...defaultProps} />);

    expect(screen.getByText('Auto-Answer Settings')).toBeInTheDocument();

    // display privacy protection message about private metrics
    expect(screen.getByText('Privacy Protection:')).toBeInTheDocument();
    expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();

    // display the privacy alert
    const alert = screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC).closest('.alert');
    expect(alert).toBeInTheDocument();

    // display the info icon in the privacy alert
    const infoIcon = screen
      .getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)
      .closest('.alert')
      ?.querySelector('i.fal.fa-info-circle');
    expect(infoIcon).toBeInTheDocument();
  });

  it('should render privacy message even when canAccessAIDocumentLibrary is false', () => {
    renderWithRouter(<SettingsModal {...defaultProps} canAccessAIDocumentLibrary={false} />);

    expect(screen.getByText('Privacy Protection:')).toBeInTheDocument();
    expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();
  });

  it('should not render the modal when isOpen is false', () => {
    renderWithRouter(<SettingsModal {...defaultProps} isOpen={false} />);

    expect(screen.queryByText('Auto-Answer Settings')).not.toBeInTheDocument();
    expect(screen.queryByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).not.toBeInTheDocument();
  });

  it('should render all form elements along with privacy message', () => {
    renderWithRouter(<SettingsModal {...defaultProps} />);

    // Check that the privacy message doesn't interfere with other elements
    expect(screen.getByText('Answer using AI')).toBeInTheDocument();
    expect(screen.getByTestId('auto-answer-useDocumentLibrary')).toBeInTheDocument();
    expect(screen.getByTestId('auto-answer-isOverwriteMetric')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();

    // And the privacy message is still there
    expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();
  });
});
