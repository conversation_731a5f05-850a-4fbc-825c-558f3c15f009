import { AIAutoAnswerSetting, SettingOption, useGetAIDocumentScanQuery } from '@api/ai';
import { AI_MESSAGE } from '@constants/ai';
import { ROUTES } from '@constants/routes';
import { QUESTION } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { generateUrl } from '@routes/util';
import { isFinishedJob, isProcessingJob } from '@utils/background-job';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Button, FormGroup, Input, Label, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';

interface Props {
  isOpen: boolean;
  toggle: () => void;
  initiativeId: string;
  handleSubmit: (settings: AIAutoAnswerSetting) => void;
  canAccessAIDocumentLibrary: boolean;
}

const SettingOptionDescription = ({
  option,
  initiativeId,
  isScanning,
  canAccessAIDocumentLibrary
}: {
  option: SettingOption;
  initiativeId: string;
  isScanning: boolean;
  canAccessAIDocumentLibrary: boolean;
}) => {
  if (option === SettingOption.OverwriteMetric) {
    return (
      <>
        <div className='text-ThemeWarningDark'>Overwrite {QUESTION.PLURAL} that already have content/answers</div>
        <div className='text-ThemeTextMedium text-sm'>
          (be careful with this one, it could overwrite content someone else has added)
        </div>
      </>
    );
  }
  if (!canAccessAIDocumentLibrary) {
    return (
      <>
        <div className='text-ThemeTextLight'>Enhance with information from Document Library</div>
        <div className='text-ThemeTextMedium text-sm'>
          (to use enable AI access in{' '}
          <Link to={generateUrl(ROUTES.ACCOUNT_SETTINGS, { initiativeId, page: 'account-management' })}>
            Admin/Account Settings/Account Management
          </Link>
          )
        </div>
      </>
    );
  }
  if (isScanning) {
    return (
      <>
        <div className='text-ThemeTextLight'>Enhance with information from Document Library</div>
        <div className='text-ThemeWarningDark text-sm'>(files currently processing - results may be incomplete)</div>
      </>
    );
  }
  return <div className='text-ThemeTextLight'>Enhance with information from Document Library</div>;
};

export const SettingsModal = ({ isOpen, toggle, initiativeId, handleSubmit, canAccessAIDocumentLibrary }: Props) => {
  const [settings, setSettings] = useState<{ [key in SettingOption]: boolean }>({
    [SettingOption.DocumentLibrary]: canAccessAIDocumentLibrary,
    [SettingOption.OverwriteMetric]: false,
  });
  const [skipPolling, setSkipPolling] = useState(false);
  const { data, isSuccess, error } = useGetAIDocumentScanQuery(initiativeId, {
    skip: skipPolling,
    pollingInterval: 10000,
  });

  useEffect(() => {
    // Skip polling when not found job or job is finished
    if (isSuccess && (!data?.job || isFinishedJob(data.job))) {
      setSkipPolling(true);
    }
  }, [data, isSuccess]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    const name = e.target.name as SettingOption;
    setSettings({ ...settings, [name]: checked });
  };

  const isScanning = !!data?.job && isProcessingJob(data.job);

  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>Auto-Answer Settings</ModalHeader>
      <ModalBody className='pb-0'>
        {error ? <BasicAlert type='danger'>{error.message}</BasicAlert> : null}
        <div>
          Answer using AI{' '}
          <span className='text-ThemeTextMedium text-sm'>(will draft answers even when no data is available)</span>
        </div>
        {[SettingOption.DocumentLibrary, SettingOption.OverwriteMetric].map((option) => (
          <FormGroup check className='mt-2 d-flex gap-2 align-items-center' key={option}>
            <Input
              type='checkbox'
              id={option}
              name={option}
              onChange={onChange}
              checked={settings[option]}
              data-testid={`auto-answer-${option}`}
              disabled={option === SettingOption.DocumentLibrary && !canAccessAIDocumentLibrary}
            />
            <Label for={option} className='m-0'>
              <SettingOptionDescription
                option={option}
                initiativeId={initiativeId}
                isScanning={isScanning}
                canAccessAIDocumentLibrary={canAccessAIDocumentLibrary}
              />
            </Label>
          </FormGroup>
        ))}
        <BasicAlert type='info' className='mt-3 px-3 py-2'>
          <div className='d-flex align-items-start'>
            <i className='fal fa-info-circle mr-2 mt-1' />
            <div className='text-sm'>
              <strong>Privacy Protection:</strong> {AI_MESSAGE.EXCLUDED_PRIVATE_METRIC}
            </div>
          </div>
        </BasicAlert>
      </ModalBody>
      <ModalFooter className='pt-3'>
        <Button color='link-secondary' className='me-3' onClick={toggle}>
          Cancel
        </Button>
        <Button data-testid='submit-auto-answer' color='primary' onClick={() => handleSubmit(settings)}>
          Save
        </Button>
      </ModalFooter>
    </Modal>
  );
};
