import { fireEvent, screen, waitFor } from '@testing-library/react';
import { AIAutoAnswer } from './index';
import { useAiAutoAnswerSurveyMutation, useGetAIAutoAnswerSurveyQuery, useGetAIDocumentScanQuery } from '@api/ai';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { ProgressStats } from '@utils/survey';
import { renderWithProviders } from '@fixtures/utils';
import { reducer } from '@reducers/index';
import { createUser, getCurrentUserState } from '@fixtures/user-factory';
import { configureStore } from '@reduxjs/toolkit';
import { Mock } from 'vitest';
import { TaskStatus, TaskType } from '@g17eco/types/background-jobs';
import { surveyOne } from '@fixtures/survey-factory';
import { initiativeOne } from '@fixtures/initiative-factory';
import { AI_MESSAGE } from '@constants/ai';

// Mock hooks
vi.mock(import('@api/ai'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useAiAutoAnswerSurveyMutation: vi.fn(),
    useGetAIAutoAnswerSurveyQuery: vi.fn(),
    useGetAIDocumentScanQuery: vi.fn(),
  };
});
vi.mock('@hooks/useSiteAlert', () => ({
  useSiteAlert: vi.fn(),
}));

describe('AIAutoAnswer', () => {
  let autoAnswerSurveyMock: any;

  const progressStats: ProgressStats = {
    created: 5,
    updated: 0,
    completed: 0,
    count: 5,
    verified: 0,
    rejected: 0,
  };

  const staffUserState = getCurrentUserState(createUser({ isStaff: true }));
  const staffRenderOptions = {
    store: configureStore({
      reducer,
      preloadedState: {
        currentUser: staffUserState,
      },
    }),
    route: { path: '/', initialEntries: ['/'] },
  };

  beforeEach(() => {
    autoAnswerSurveyMock = vi.fn().mockReturnValue({
      unwrap: vi.fn().mockResolvedValue({}),
    });
    (useAiAutoAnswerSurveyMutation as Mock).mockReturnValue([
      autoAnswerSurveyMock,
      { data: undefined, isLoading: false },
    ]);
    (useSiteAlert as Mock).mockReturnValue({
      addSiteError: vi.fn(),
    });
    (useGetAIDocumentScanQuery as Mock).mockReturnValue({
      isSuccess: true,
      data: undefined,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should not render for completed survey', () => {
    (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
      isSuccess: true,
      data: {},
    });

    renderWithProviders(
      <AIAutoAnswer
        initiativeId={initiativeOne._id}
        survey={{ ...surveyOne, completedDate: new Date().toISOString() }}
        progressStats={progressStats}
        canAccessAIDocumentLibrary
      />,
      staffRenderOptions,
    );
    expect(screen.queryByText(/Auto Answer/)).to.be.null;
  });

  it('should render Auto Answer button when no job is running', () => {
    (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
      isSuccess: true,
      data: {},
    });

    renderWithProviders(
      <AIAutoAnswer
        initiativeId={initiativeOne._id}
        survey={surveyOne}
        progressStats={progressStats}
        canAccessAIDocumentLibrary
      />,
      staffRenderOptions,
    );
    expect(screen.getByText('Auto Answer')).to.exist;
  });

  it('should show "Auto Answering..." button when a job is processing', () => {
    (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
      isSuccess: true,
      data: {
        job: {
          tasks: [{ type: TaskType.AIAutoAnswerProcess, status: TaskStatus.Pending }],
        },
      },
    });

    renderWithProviders(
      <AIAutoAnswer
        initiativeId={initiativeOne._id}
        survey={surveyOne}
        progressStats={progressStats}
        canAccessAIDocumentLibrary
      />,
      staffRenderOptions,
    );
    expect(screen.getByText('Auto Answering...')).to.exist;
  });

  it('should call autoAnswerSurvey when settings modal submit', async () => {
    (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
      isSuccess: true,
      data: {},
    });

    renderWithProviders(
      <AIAutoAnswer
        initiativeId={initiativeOne._id}
        survey={surveyOne}
        progressStats={progressStats}
        canAccessAIDocumentLibrary
      />,
      staffRenderOptions,
    );

    const autoAnswerButton = screen.getByText('Auto Answer');
    fireEvent.click(autoAnswerButton);

    const submitButton = screen.getByText('Save');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(autoAnswerSurveyMock).toHaveBeenCalledTimes(1);
    });
  });

  it('should show site error if API call fails', async () => {
    const errorMock = { message: 'Something went wrong' };

    (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
      isSuccess: false,
      error: errorMock,
    });

    const addSiteErrorMock = vi.fn();
    (useSiteAlert as Mock).mockReturnValue({ addSiteError: addSiteErrorMock });

    renderWithProviders(
      <AIAutoAnswer
        initiativeId={initiativeOne._id}
        survey={surveyOne}
        progressStats={progressStats}
        canAccessAIDocumentLibrary
      />,
      staffRenderOptions,
    );

    await waitFor(() => {
      expect(addSiteErrorMock).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('Private Metrics Messaging', () => {
    it('should display private metrics exclusion message in settings modal', async () => {
      (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
        isSuccess: true,
        data: {},
      });

      renderWithProviders(
        <AIAutoAnswer
          initiativeId={initiativeOne._id}
          survey={surveyOne}
          progressStats={progressStats}
          canAccessAIDocumentLibrary
        />,
        staffRenderOptions,
      );

      // Open settings modal
      const settingsButton = screen.getByTestId('auto-answer-btn');
      fireEvent.click(settingsButton);

      // Check that privacy message is displayed
      await waitFor(() => {
        expect(screen.getByText('Privacy Protection:')).toBeInTheDocument();
        expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();
      });
    });

    it('should show privacy message even when document library is not accessible', async () => {
      (useGetAIAutoAnswerSurveyQuery as Mock).mockReturnValue({
        isSuccess: true,
        data: {},
      });

      renderWithProviders(
        <AIAutoAnswer
          initiativeId={initiativeOne._id}
          survey={surveyOne}
          progressStats={progressStats}
          canAccessAIDocumentLibrary={false}
        />,
        staffRenderOptions,
      );

      // Open settings modal
      const settingsButton = screen.getByTestId('auto-answer-btn');
      fireEvent.click(settingsButton);

      // Check that privacy message is still displayed
      await waitFor(() => {
        expect(screen.getByText('Privacy Protection:')).toBeInTheDocument();
        expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();
      });
    });
  });
});
