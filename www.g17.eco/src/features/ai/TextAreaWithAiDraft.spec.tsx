import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { TextAreaWithAIDraft } from './TextAreaWithAiDraft';
import { generateErrorToast, generateToast } from '@g17eco/molecules/toasts';

vi.mock('@g17eco/molecules/toasts', () => ({
  generateToast: vi.fn(),
  generateErrorToast: vi.fn(),
}));

const createTestSetup = (props: any) => {
  const user = userEvent.setup();
  const renderResult = render(<TextAreaWithAIDraft {...props} />);
  return { user, ...renderResult };
};

describe('TextAreaWithAIDraft', () => {
  const mockOnChange = vi.fn();

  const defaultProps = {
    onChange: mockOnChange,
    value: '',
    canUseAIDraft: true,
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('canUseAIDraft prop', () => {
    it('should render the "AI draft" button when canUseAIDraft is true', () => {
      createTestSetup({ ...defaultProps, getDraft: vi.fn() });
      expect(screen.getByRole('button', { name: /ai draft/i })).toBeInTheDocument();
    });
  
    it('should not render the "AI draft" button when canUseAIDraft is false', () => {
      createTestSetup({ ...defaultProps, getDraft: vi.fn(), canUseAIDraft: false });
      expect(screen.queryByRole('button', { name: /ai draft/i })).not.toBeInTheDocument();
    });
  });


  describe('getDraft functionality', () => {
    it('should call generateToast when getDraft returns a response with no content', async () => {
      const mockGetDraft = vi.fn().mockResolvedValue({ content: null });
      const { user } = createTestSetup({ ...defaultProps, getDraft: mockGetDraft });

      const draftButton = screen.getByRole('button', { name: /ai draft/i });
      await user.click(draftButton);

      await waitFor(() => {
        expect(generateToast).toHaveBeenCalledWith({
          color: 'warning',
          title: 'AI draft can\'t respond',
          message:
            'AI couldn\'t provide a confident answer based on the available information. Please respond manually.',
        });
      });
    });

    it('should call generateErrorToast when getDraft promise is rejected', async () => {
      const mockGetDraft = vi.fn().mockRejectedValue(new Error('API Error'));
      const { user } = createTestSetup({ ...defaultProps, getDraft: mockGetDraft });

      const draftButton = screen.getByRole('button', { name: /ai draft/i });
      await user.click(draftButton);

      await waitFor(() => {
        expect(generateErrorToast).toHaveBeenCalledWith({
          title: 'AI draft can\'t respond',
          message: 'Unable to get AI draft. Please try again or contact our support team',
        });
      });
    });
  });
});
