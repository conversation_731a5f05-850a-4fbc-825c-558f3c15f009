/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { useRef, useState, TextareaHTMLAttributes, ReactNode } from 'react';
import { useAutosizeTextArea } from './hooks/useAutosizeTextArea';
import { useTypewriter } from './hooks/useTypewriter';
import { Input } from 'reactstrap';
import { AIResponse } from '../../types/ai';
import { AILoadingPlaceholder } from './AILoadingPlaceholder';
import { ButtonGradient } from '@g17eco/atoms/button';
import { EditorState } from 'lexical';
import { RichTextEditor } from '@features/rich-text-editor';
import { AIDisclaimer } from '@g17eco/molecules/ai-disclaimer';
import { generateErrorToast, generateToast } from '@g17eco/molecules/toasts';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface DraftButtonConfig {
  disabled?: boolean;
  tooltip?: string;
}

interface AiDraft extends Omit<TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> {
  getDraft: () => Promise<Pick<AIResponse, 'content'>>;
  onChange: (value: string, editorState?: EditorState) => void;
  canUseRichTextEditor?: boolean;
  onEditorAIWrite?: (output: string) => void;
  renderButton?: (props: {
    disabled: boolean;
    isDrafting: boolean;
    getDraft: () => void;
    setAppendString: (value: string) => void;
    setIsLoading: (value: boolean) => void;
    setIsReplacing: (value: boolean) => void;
    currentValue?: string;
  }) => ReactNode;
  canUseAIDraft: boolean;
  draftConfig?: DraftButtonConfig;
}

export const TextAreaWithAIDraft = (props: AiDraft) => {
  const {
    canUseRichTextEditor = false,
    onChange,
    onEditorAIWrite,
    name,
    className,
    maxLength,
    rows,
    renderButton,
    canUseAIDraft,
    draftConfig,
  } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [appendString, setAppendString] = useState<string | null>(null);
  const textAreaRef = useRef(null);

  // Track if we're replacing text (for refine) vs appending (for initial draft)
  const [isReplacing, setIsReplacing] = useState(false);

  const typewriter = useTypewriter({
    initialStr: isReplacing ? '' : `${props.value ?? ''}`,
    appendStr: appendString,
    onWrite: (output: string) => {
      onChange(output);
      if (isReplacing) {
        setIsReplacing(false); // Reset after replacing
      }
    },
    onEditorAIWrite: onEditorAIWrite,
  });

  const isWriting = typewriter.isWriting;
  const value = `${typeof props.value === 'string' ? (props.value ?? '') : ''}${typewriter.liveResult ?? ''}`;
  useAutosizeTextArea(textAreaRef.current, value);

  const getDraft = () => {
    if (!canUseAIDraft || isLoading) {
      return;
    }
    setIsLoading(true);
    props
      .getDraft()
      .then((response) => {
        if (response.content) {
          const prefix = props.value ? '\n\n' : '';
          setAppendString(`${prefix}${response.content}`);
          return;
        }
        generateToast({
          color: 'warning',
          title: 'AI draft can\'t respond',
          message:
            'AI couldn\'t provide a confident answer based on the available information. Please respond manually.',
        });
      })
      .catch(() => {
        generateErrorToast({
          title: 'AI draft can\'t respond',
          message: 'Unable to get AI draft. Please try again or contact our support team',
        });
      })
      .finally(() => setIsLoading(false));
  };

  const isDrafting = isLoading || isWriting;
  const disabled = props.disabled || isDrafting;
  const showDisclaimer = Boolean(isDrafting || typewriter.finalResult);

  const onEditorChange = (plainText: string, editorState: EditorState) => {
    if (isDrafting) {
      return;
    }
    onChange(plainText, editorState);
  };

  return (
    <>
      <AILoadingPlaceholder isLoading={isLoading} isWriting={isWriting}>
        <div className='w-100 position-relative'>
          {canUseRichTextEditor ? (
            <RichTextEditor handleChange={({ plainText, editorState }) => onEditorChange(plainText, editorState)} />
          ) : (
            <Input
              type='textarea'
              innerRef={textAreaRef}
              name={name}
              className={`w-100 ${className} dont_translate`}
              maxLength={maxLength}
              rows={rows}
              onChange={(e) => {
                setAppendString('');
                onChange(e.target.value);
              }}
              disabled={disabled}
              value={value}
            />
          )}
          {canUseAIDraft ? (
            <div className='position-absolute' style={{ right: '10px', bottom: '10px' }}>
              {renderButton ? (
                renderButton({
                  disabled,
                  isDrafting,
                  getDraft,
                  setAppendString,
                  setIsLoading,
                  setIsReplacing,
                  currentValue: props.value as string,
                })
              ) : (
                <SimpleTooltip text={draftConfig?.tooltip || ''}>
                  <ButtonGradient
                    disabled={disabled || draftConfig?.disabled}
                    color='gradient'
                    outline={!isDrafting}
                    onClick={() => getDraft()}
                  >
                    <i className='fal fa-sparkles mr-2' />
                    {isDrafting ? 'Drafting...' : 'AI draft'}
                  </ButtonGradient>
                </SimpleTooltip>
              )}
            </div>
          ) : null}
        </div>
      </AILoadingPlaceholder>
      {showDisclaimer ? <AIDisclaimer /> : null}
    </>
  );
};
