import { useGetAIUtrvFurtherNotesDraftMutation, useGetAIUtrvFurtherNotesDraftRefineMutation } from '@api/ai';
import { CopyButton } from '@features/copy';
import { SurveyModelMinimalUtrv } from '@g17eco/types/survey';
import { useEffect, useMemo } from 'react';
import { QueryError } from '@g17eco/molecules/query/QueryError';
import { SectionLoader } from '@g17eco/atoms/loader';
import { AIRefineButton, HandleRefineData } from './AIRefineButton';
import { AI_MESSAGE } from '@constants/ai';
import { AI_ASSISTANT_SIDEBAR_ID } from '../constants';

interface Props {
  utrCode: string;
  utrv: SurveyModelMinimalUtrv;
}
export const FurtherNotes = ({ utrCode, utrv }: Props) => {
  const [
    getFurtherNotesDraft,
    { isLoading: isGeneratingDraft, data: draftData, isError: isDraftError, error: draftError },
  ] = useGetAIUtrvFurtherNotesDraftMutation();
  const [
    getFurtherNotesDraftRefine,
    { isLoading: isRefining, data: refineData, isError: isRefineError, error: refineError },
  ] = useGetAIUtrvFurtherNotesDraftRefineMutation();

  const isPrivate = utrv.isPrivate;

  const suggestedFurtherNotes = useMemo(() => {
    if (isPrivate) {
      return AI_MESSAGE.EXCLUDED_PRIVATE_METRIC;
    }
    return refineData?.content ?? draftData?.content ?? '';
  }, [draftData, refineData, isPrivate]);

  useEffect(() => {
    // Skip API call if metric is private
    if (!isPrivate) {
      getFurtherNotesDraft({ initiativeId: utrv.initiativeId, utrvId: utrv._id, draftData: utrv });
    }
  }, [getFurtherNotesDraft, utrv, isPrivate]);

  const getError = () => {
    const error = isDraftError ? draftError : isRefineError ? refineError : null;
    return error ? <QueryError error={error} type={'danger'} /> : null;
  };

  return (
    <div>
      <div className='d-flex justify-content-between align-items-center'>
        <h6 className='my-0 text-ThemeHeadingDark'>AI Draft Further Explanation/Notes:</h6>
        {!isPrivate && (
          <div className='flex-grow-0 d-flex gap-1 align-items-center'>
            <CopyButton content={suggestedFurtherNotes} />
            <AIRefineButton
              handleRefine={(data: HandleRefineData) => {
                getFurtherNotesDraftRefine({
                  initiativeId: utrv.initiativeId,
                  utrvId: utrv._id,
                  draftData: utrv,
                  action: data.action,
                  additionalContext: data.additionalContext,
                  textToRefine: suggestedFurtherNotes,
                });
              }}
              utrCode={utrCode}
              textContent={suggestedFurtherNotes}
              modalTargetId={AI_ASSISTANT_SIDEBAR_ID}
            />
          </div>
        )}
      </div>
      {getError()}
      {!isPrivate && (isGeneratingDraft || isRefining) ? (
        <SectionLoader />
      ) : (
        <p className='text-ThemeTextMedium'>{suggestedFurtherNotes}</p>
      )}
    </div>
  );
};
