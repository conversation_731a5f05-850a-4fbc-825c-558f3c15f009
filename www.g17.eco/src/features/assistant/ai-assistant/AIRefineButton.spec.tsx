import { screen, waitFor } from '@testing-library/react';
import { setup } from '@fixtures/utils';
import { simpleUserStore } from '@fixtures/redux-store';
import { AIRefineButton } from './AIRefineButton';
import { RefineAction } from '@api/ai';
import { SettingStorage } from '@services/SettingStorage';
import { vi } from 'vitest';

// Mock SettingStorage
vi.mock('@services/SettingStorage', () => {
  const storage = new Map<string, string>();
  return {
    SettingStorage: {
      getItem: vi.fn((key: string) => storage.get(key)),
      setItem: vi.fn((key: string, value: string) => storage.set(key, value)),
      clear: () => storage.clear(),
    }
  };
});

describe('AIRefineButton', () => {
  const defaultProps = {
    handleRefine: vi.fn(),
    textContent: 'Sample text content',
    utrCode: 'TEST_UTR_001',
  };

  const renderComponent = (props = {}) => {
    return setup(
      <AIRefineButton {...defaultProps} {...props} />,
      { store: simpleUserStore }
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Clear the mocked storage
    const storage = vi.mocked(SettingStorage);
    (storage as any).clear();
  });

  describe('Dropdown Rendering', () => {
    it('should render the AI refine button with stars icon', () => {
      renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      expect(button).toBeInTheDocument();
      // Button should have the transparent color class
      expect(button).toHaveClass('btn-transparent');
    });

    it('should open dropdown menu when clicked', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      expect(screen.getByText('Rewrite')).toBeInTheDocument();
      expect(screen.getByText('Shorten')).toBeInTheDocument();
      expect(screen.getByText('AI Instructions')).toBeInTheDocument();
    });

    it('should show "Write" instead of "Rewrite" when no content exists', async () => {
      const { user } = renderComponent({ textContent: '' });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      expect(screen.getByText('Write')).toBeInTheDocument();
      expect(screen.queryByText('Rewrite')).not.toBeInTheDocument();
    });

    it('should disable Shorten option when no content exists', async () => {
      const { user } = renderComponent({ textContent: '' });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const shortenOption = screen.getByText('Shorten').closest('button');
      expect(shortenOption).toHaveAttribute('disabled');
    });

    it('should enable Shorten option when content exists', async () => {
      const { user } = renderComponent({ textContent: 'Some content' });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const shortenOption = screen.getByText('Shorten').closest('button');
      expect(shortenOption).not.toHaveAttribute('disabled');
    });
  });

  describe('Refine Actions', () => {
    it('should call handleRefine with Rewrite action', async () => {
      const handleRefine = vi.fn();
      const { user } = renderComponent({ handleRefine });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const rewriteOption = screen.getByText('Rewrite');
      await user.click(rewriteOption);

      expect(handleRefine).toHaveBeenCalledWith({
        action: RefineAction.Rewrite,
        additionalContext: '',
        aiModel: 'gpt-4o',
      });
    });

    it('should call handleRefine with Shorten action', async () => {
      const handleRefine = vi.fn();
      const { user } = renderComponent({ handleRefine });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const shortenOption = screen.getByText('Shorten');
      await user.click(shortenOption);

      expect(handleRefine).toHaveBeenCalledWith({
        action: RefineAction.Shorten,
        additionalContext: '',
        aiModel: 'gpt-4o',
      });
    });

    it('should use stored context when calling handleRefine', async () => {
      const handleRefine = vi.fn();
      const storedData = {
        additionalContext: 'Stored instructions',
        aiModel: 'gpt-4o',
      };

      SettingStorage.setItem('ai-instructions-TEST_UTR_001', JSON.stringify(storedData));

      const { user } = renderComponent({ handleRefine });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const rewriteOption = screen.getByText('Rewrite');
      await user.click(rewriteOption);

      expect(handleRefine).toHaveBeenCalledWith({
        action: RefineAction.Rewrite,
        additionalContext: 'Stored instructions',
        aiModel: 'gpt-4o',
      });
    });
  });

  describe('AI Instructions Modal', () => {
    it('should open modal when AI Instructions is clicked', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('AI Instructions', { selector: 'h5' })).toBeInTheDocument();
    });

    it('should display textarea for AI instructions', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      const textarea = screen.getByPlaceholderText(/Pass additional information/);
      expect(textarea).toBeInTheDocument();
      expect(textarea).toHaveAttribute('maxLength', '500');
    });

    it('should display model selector (disabled)', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      expect(screen.getByText('Model')).toBeInTheDocument();
      expect(screen.getByText('GPT-4o')).toBeInTheDocument();

      // The select component should be present with GPT-4o selected
      const gptOption = screen.getByText('GPT-4o');
      expect(gptOption).toBeInTheDocument();

      // The container for the select should have disabled styling
      const selectContainer = gptOption.closest('[class*="control"]');
      expect(selectContainer).toBeInTheDocument();
    });

    it('should close modal when cancel button is clicked', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      expect(screen.getByRole('dialog')).toBeInTheDocument();

      const closeButton = screen.getByRole('button', { name: 'Close' });
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('should allow entering instructions without submit button', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      const textarea = screen.getByPlaceholderText(/Pass additional information/);
      await user.type(textarea, 'Custom instructions');

      // Verify textarea has the content
      expect(textarea).toHaveValue('Custom instructions');
    });

    it('should auto-save instructions to localStorage when modal is closed', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      const textarea = screen.getByPlaceholderText(/Pass additional information/);
      await user.type(textarea, 'Custom instructions');

      // Close the modal by clicking the close button
      const closeButton = screen.getByRole('button', { name: 'Close' });
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });

      const storedData = SettingStorage.getItem('ai-instructions-TEST_UTR_001');
      const parsedData = JSON.parse(storedData || '{}');

      expect(parsedData.additionalContext).toBe('Custom instructions');
      expect(parsedData.aiModel).toBe('gpt-4o');
    });

    it('should load previously saved instructions when opening modal', async () => {
      const storedData = {
        additionalContext: 'Previously saved instructions',
        aiModel: 'gpt-4o',
      };

      SettingStorage.setItem('ai-instructions-TEST_UTR_001', JSON.stringify(storedData));

      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      const textarea = screen.getByPlaceholderText<HTMLTextAreaElement>(/Pass additional information/);
      expect(textarea.value).toBe('Previously saved instructions');
    });
  });

  describe('Local Storage Integration', () => {
    it('should use correct storage key based on utrCode with auto-save', async () => {
      const { user } = renderComponent({ utrCode: 'CUSTOM_UTR_123' });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      const textarea = screen.getByPlaceholderText(/Pass additional information/);
      await user.type(textarea, 'Test instructions');

      // Close the modal to trigger auto-save
      const closeButton = screen.getByRole('button', { name: 'Close' });
      await user.click(closeButton);

      const storedData = SettingStorage.getItem('ai-instructions-CUSTOM_UTR_123');
      expect(storedData).toBeTruthy();
      const parsedData = JSON.parse(storedData || '{}');
      expect(parsedData.additionalContext).toBe('Test instructions');
    });

    it('should auto-save default values when modal is closed with no new content', async () => {
      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      // Close the modal without entering any content
      const closeButton = screen.getByRole('button', { name: 'Close' });
      await user.click(closeButton);

      // Should save default content
      const storedData = SettingStorage.getItem('ai-instructions-TEST_UTR_001');
      const parsedData = JSON.parse(storedData || '{}');
      expect(parsedData).toEqual({
        additionalContext: '',
        aiModel: 'gpt-4o',
      });
    });

    it('should clear instructions if they are removed and modal is closed', async () => {
      const storedData = {
        additionalContext: 'Previously saved instructions',
        aiModel: 'gpt-4o',
      };
      SettingStorage.setItem('ai-instructions-TEST_UTR_001', JSON.stringify(storedData));

      const { user } = renderComponent();

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const aiInstructionsOption = screen.getByText('AI Instructions');
      await user.click(aiInstructionsOption);

      const textarea = screen.getByPlaceholderText<HTMLTextAreaElement>(/Pass additional information/);
      expect(textarea.value).toBe('Previously saved instructions');

      await user.clear(textarea);
      expect(textarea.value).toBe('');

      const closeButton = screen.getByRole('button', { name: 'Close' });
      await user.click(closeButton);

      const newStoredData = SettingStorage.getItem('ai-instructions-TEST_UTR_001');
      const parsedData = JSON.parse(newStoredData || '{}');
      expect(parsedData.additionalContext).toBe('');
      expect(parsedData.aiModel).toBe('gpt-4o');
    });

    it('should handle invalid JSON in localStorage gracefully', async () => {
      SettingStorage.setItem('ai-instructions-TEST_UTR_001', 'invalid json');

      const handleRefine = vi.fn();
      const { user } = renderComponent({ handleRefine });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const rewriteOption = screen.getByText('Rewrite');
      await user.click(rewriteOption);

      // Should use default values when JSON is invalid
      expect(handleRefine).toHaveBeenCalledWith({
        action: RefineAction.Rewrite,
        additionalContext: '',
        aiModel: 'gpt-4o',
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle whitespace-only content as empty', async () => {
      const { user } = renderComponent({ textContent: '   ' });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      expect(screen.getByText('Write')).toBeInTheDocument();
      expect(screen.queryByText('Rewrite')).not.toBeInTheDocument();

      const shortenOption = screen.getByText('Shorten').closest('button');
      expect(shortenOption).toHaveAttribute('disabled');
    });

    it('should handle undefined textContent', async () => {
      const { user } = renderComponent({ textContent: undefined });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      expect(screen.getByText('Write')).toBeInTheDocument();
    });

    it('should not call handleRefine when disabled option is clicked', async () => {
      const handleRefine = vi.fn();
      const { user } = renderComponent({ handleRefine, textContent: '' });

      const button = screen.getByTestId('ai-refine-button');
      await user.click(button);

      const shortenOption = screen.getByText('Shorten').closest('button');
      await user.click(shortenOption!);

      expect(handleRefine).not.toHaveBeenCalled();
    });
  });
});
