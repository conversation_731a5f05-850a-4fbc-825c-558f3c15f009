import { useLazyGetAIUtrvAssistantQuery } from '@api/ai';
import { AnswerDataProps } from '@features/assistant/ai-assistant/Answer';
import { getAdditionalContext } from '@features/assistant/ai-assistant/utils';
import { AIResponse } from '@g17eco/types/ai';
import { createContext, useContext } from 'react';
import { InputColumn } from '@g17eco/types/questionInterfaces';
import { useAppSelector } from '@reducers/index';
import { getRootConfig } from '@selectors/globalData';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';

interface AIAssistantContextProps {
  getTextDraft: (columnCode?: string, rowToAdd?: InputColumn[]) => Promise<Pick<AIResponse, 'content'>>;
  canAccessAIMetricAssistant: boolean;
}

export const AIAssistantContext = createContext<AIAssistantContextProps>({
  getTextDraft: async () => ({ content: '' }),
  canAccessAIMetricAssistant: false,
});

export const useAIAssistantContext = () => {
  return useContext(AIAssistantContext);
};

interface Props {
  children: JSX.Element;
  initiativeId: string;
  utrvId: string | undefined;
  utr: AnswerDataProps['utr'] | undefined;
}

export const AIAssistantContextProvider = ({ children, initiativeId, utrvId, utr }: Props) => {
  const [getAIUtrvAssistant] = useLazyGetAIUtrvAssistantQuery();
  const rootConfig = useAppSelector(getRootConfig);
  const canAccessAIMetricAssistant = FeaturePermissions.canAccessAIMetricAssistant(rootConfig);

  const getTextDraft = async (columnCode?: string, currentRow?: InputColumn[]) => {
    if (!utrvId || !utr) {
      return { content: '' };
    }

    return getAIUtrvAssistant({ initiativeId, utrvId, additionalContext: getAdditionalContext({ utr, currentRow }) })
      .unwrap()
      .then((response) => {
        const predictAnswer =
          columnCode && typeof response.predictedAnswer === 'object'
            ? response.predictedAnswer?.[columnCode]
            : response.predictedAnswer;

        return { content: typeof predictAnswer === 'string' ? predictAnswer : '' };
      });
  };

  return (
    <AIAssistantContext.Provider value={{ getTextDraft, canAccessAIMetricAssistant }}>
      {children}
    </AIAssistantContext.Provider>
  );
};
