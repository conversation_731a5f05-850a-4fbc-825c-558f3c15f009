.offcanvas {
  .ai-instructions-modal-wrapper {
    position: absolute;
    width: 100%;
    transform: translate(0, -100%);
  }

  .ai-instructions-modal-backdrop {
    position: relative;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 1;
  }
  
  .ai-instructions-modal {
    position: absolute;
    top: 50%;
    left: 50%;
    max-width: 95%;
    width: 100%;
    box-shadow: none;
    border: 0;
    border-radius: 0.375rem;
    z-index: 1051;
    transform: translate(-50%, -50%);
    backdrop-filter: blur(1px);
    
    .modal-dialog {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      display: flex;
      align-items: center;
    }

    .modal-content {
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
      border-radius: 0.375rem;
    }
  }
}