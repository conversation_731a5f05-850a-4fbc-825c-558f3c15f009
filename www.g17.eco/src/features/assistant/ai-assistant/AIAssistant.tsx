import './styles.scss';
import { Answer } from './Answer';
import { DashboardDivider } from '@g17eco/atoms/divider';
import { AIDisclaimer } from '@g17eco/molecules/ai-disclaimer';
import { FurtherNotes } from './FurtherNotes';
import { AnswerProps } from './types';
import { Button } from 'reactstrap';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { AI_MESSAGE } from '@constants/ai';

export const AIAssistant = ({ utrv, utr }: AnswerProps) => {
  const { isPrivate } = utrv;
  return (
    <div className='question-assistant__ai d-flex flex-column gap-3'>
      {isPrivate ? (
        <p className='mt-3 mb-3'>{AI_MESSAGE.EXCLUDED_PRIVATE_METRIC}</p>
      ) : (
        <>
          <Answer utrv={utrv} utr={utr} />
          <FurtherNotes utrCode={utr.code} utrv={utrv} />
        </>
      )}

      <DashboardDivider className='' />

      <div className='d-flex align-items-center gap-2 text-ThemeTextLight'>
        <Button color='secondary' outline className='text-ThemeTextLight'>
          <i className='fal fa-sparkles mr-1 mt-1 fs-6 text-ThemeTextLight' />
          <span className='ms-2'>GPT-4o</span>
        </Button>
        <SimpleTooltip text='AI instructions'>
          <i className='fa-light fa-circle-info fs-5'></i>
        </SimpleTooltip>
      </div>

      <AIDisclaimer className='mt-0' />
    </div>
  );
};
