/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { useState, useMemo, useEffect } from 'react';
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  FormGroup,
  Input,
  Label,
  Modal,
  ModalBody,
  ModalHeader,
  UncontrolledDropdown,
} from 'reactstrap';
import AIEditPencil from '@g17eco/images/ai-edit-pencil.svg';
import AIShortenCut from '@g17eco/images/ai-shorten-cut.svg';
import OpenAiIcon from '@g17eco/images/openai.svg';
import { RefineAction } from '@api/ai';
import { SettingStorage } from '@services/SettingStorage';
import { safeJsonParse } from '@utils/json';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import './AIRefineButton.scss';

enum AIModel {
  GPT_4o = 'gpt-4o',
  Claude_3_5 = 'claude-3.5',
}

export interface HandleRefineData {
  action: RefineAction;
  additionalContext?: string;
  aiModel?: AIModel;
}

const aiModelOptions = [
  {
    label: (
      <span>
        <img src={OpenAiIcon} alt='openai' className='me-1' /> GPT-4o
      </span>
    ),
    value: AIModel.GPT_4o,
  },
];

interface Props {
  handleRefine: (data: HandleRefineData) => void;
  textContent: string | undefined;
  utrCode: string;
  modalTargetId?: string;
  disabled?: boolean;
}
export const AIRefineButton = ({ handleRefine, textContent = '', utrCode, disabled = false, modalTargetId }: Props) => {
  const [form, setForm] = useState<Pick<HandleRefineData, 'additionalContext' | 'aiModel'>>();

  const hasContent = textContent.trim().length > 0;
  // Store the AI instructions in local storage for the given Universal Tracker (utrCode),
  // therefore it will be reused for all AI Refine functionality like notes/answer.
  const storageKey = `ai-instructions-${utrCode}`;

  const options = useMemo(
    () => [
      {
        label: (
          <span>
            <img src={AIEditPencil} alt={hasContent ? 'Rewrite' : 'Write'} className='mr-2' />{' '}
            {hasContent ? 'Rewrite' : 'Write'}
          </span>
        ),
        value: RefineAction.Rewrite,
      },
      {
        label: (
          <span>
            <img src={AIShortenCut} alt='Shorten' className='mr-2' /> Shorten
          </span>
        ),
        value: RefineAction.Shorten,
        disabled: !hasContent,
      },
      {
        label: (
          <span>
            <i className='fa-light fa-input-text mr-2' /> AI Instructions
          </span>
        ),
        value: RefineAction.AddContext,
      },
    ],
    [hasContent],
  );

  const toggle = () => {
    // Auto-save when closing the modal
    SettingStorage.setItem(
      storageKey,
      JSON.stringify({ ...form, additionalContext: form?.additionalContext?.trim() || '' }),
    );
    setForm(undefined);
  };

  const handleSelect = (action: RefineAction) => {
    const contextData = SettingStorage.getItem(storageKey);
    const { additionalContext = '', aiModel = AIModel.GPT_4o } = safeJsonParse<{
      additionalContext?: string;
      aiModel?: AIModel;
    }>(contextData, {});

    if (action === RefineAction.AddContext) {
      setForm({ additionalContext, aiModel });
      return;
    }

    handleRefine({ action, additionalContext, aiModel });
  };

  const onChangeForm = (data: Partial<Pick<HandleRefineData, 'additionalContext' | 'aiModel'>>) => {
    setForm((prev) => {
      const newForm = {
        additionalContext: prev?.additionalContext,
        aiModel: prev?.aiModel ?? AIModel.GPT_4o,
        ...data,
      };
      return newForm;
    });
  };

  return (
    <>
      <UncontrolledDropdown>
        <DropdownToggle color='transparent' className='fs-6 px-1' disabled={disabled} data-testid='ai-refine-button'>
          <i className='fa-light fa-stars'></i>
        </DropdownToggle>
        <DropdownMenu>
          {options.map((option) => (
            <DropdownItem
              key={option.value}
              onClick={() => !option.disabled && handleSelect(option.value)}
              disabled={option.disabled}
            >
              {option.label}
            </DropdownItem>
          ))}
        </DropdownMenu>
      </UncontrolledDropdown>

      <Modal
        isOpen={!!form}
        toggle={toggle}
        backdrop='static'
        wrapClassName='ai-instructions-modal-wrapper'
        backdropClassName='ai-instructions-modal-backdrop'
        modalClassName='ai-instructions-modal'
        container={modalTargetId}
      >
        <ModalHeader toggle={toggle}>AI Instructions</ModalHeader>
        <ModalBody>
          <FormGroup>
            <Label for='aiInstruction'>AI Instructions</Label>
            <Input
              id='aiInstruction'
              type='textarea'
              rows={4}
              maxLength={500}
              placeholder='Pass additional information or instructions to the AI to change the tone or improve its response by giving it extra data. Select Rewrite to send this info over.'
              value={form?.additionalContext ?? ''}
              onChange={(e) => onChangeForm({ additionalContext: e.target.value })}
            />
          </FormGroup>
          <FormGroup>
            <Label for='aiModel'>Model</Label>
            <SimpleTooltip text='New models coming soon'>
              <SelectFactory
                selectType={SelectTypes.SingleSelect}
                options={aiModelOptions}
                value={aiModelOptions.find((op) => op.value === form?.aiModel) ?? null}
                onChange={(e) => onChangeForm({ aiModel: e?.value })}
                isDisabled
              />
            </SimpleTooltip>
          </FormGroup>
        </ModalBody>
      </Modal>
    </>
  );
};
