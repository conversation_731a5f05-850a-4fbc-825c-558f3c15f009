/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { loadCustomMetricGroupsByInitiativeId, reloadCustomMetricGroupsByInitiativeId } from '@actions/initiative';
import { loadInitiativeQuestions, reloadInitiativeQuestions } from '@actions/blueprints';
import { Loader } from '@g17eco/atoms/loader';
import { CustomMetricsComponent } from '@features/custom-metrics/CustomMetricsComponent';
import { generateUrl } from '@routes/util';
import CustomMetricContainer, { getBaseRoute } from '@features/custom-metrics/CustomMetricContainer';
import { handleUnloadSurvey } from '@actions/survey';
import queryString from 'query-string';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { CustomMetricsViewMode } from '@features/custom-metrics/constants';
import { loggerMessage } from '../../logger';
import { CustomMetricsUsage } from '@g17eco/types/metricGroup';
import { CustomMetricRouteParams } from './types';

interface Props {
  initiativeId: string;
  isPortfolioTracker?: boolean;
  customMetricsUsage: CustomMetricsUsage;
  refetchMetricsUsage: () => void;
  groupId?: string;
  view?: string;
  buttons?: React.JSX.Element[];
}

export const CustomMetrics = (props: Props) => {
  const { initiativeId, customMetricsUsage, refetchMetricsUsage, groupId, view, isPortfolioTracker = false, buttons } = props;
  const history = useHistory();
  const dispatch = useAppDispatch();
  const location = useLocation();
  const query = queryString.parse(location.search);
  const surveyId = Array.isArray(query.surveyId) ? query.surveyId[0] : query.surveyId;

  const { data: metricGroups, loaded: metricGroupsLoaded, initiativeId: metricGroupsInitiativeId } = useAppSelector((state) => state.customMetricGroups);
  const { data: blueprintQuestions, loaded: blueprintQuestionLoaded } = useAppSelector((state) => state.initiativeBlueprintQuestions);

  const isLoaded = metricGroupsLoaded && metricGroupsInitiativeId === initiativeId;

  const handleChangeMetricGroup = React.useCallback((updateParams: CustomMetricRouteParams) => {
    const params: { portfolioId?: string, initiativeId?: string, groupId?: string, view?: string } = {
      portfolioId: initiativeId,
      initiativeId: initiativeId,
    };
    if (updateParams.groupId) {
      params.groupId = updateParams.groupId;
    }
    if (updateParams.view) {
      params.view = updateParams.view;
    }

    const baseRoute = getBaseRoute(isPortfolioTracker);
    history.push({
      pathname: generateUrl(baseRoute, params),
      search: location.search
    });
  }, [initiativeId, location.search, isPortfolioTracker, history]);

  const handleReload = React.useCallback(
    async (firstRun = false) => {
      try {
        if (initiativeId) {
          if (blueprintQuestionLoaded) {
            if (firstRun && initiativeId === metricGroupsInitiativeId) {
              // Don't need to do this twice if it was loaded by another component
              return;
            }
            dispatch(handleUnloadSurvey());
            dispatch(reloadInitiativeQuestions(initiativeId));
            dispatch(reloadCustomMetricGroupsByInitiativeId(initiativeId));
          } else {
            dispatch(handleUnloadSurvey());
            dispatch(loadInitiativeQuestions(initiativeId));
            dispatch(loadCustomMetricGroupsByInitiativeId(initiativeId));
          }

          refetchMetricsUsage();
        }
      } catch (e) {
        loggerMessage(e.message, { initiativeId });
      }
    },
    [initiativeId, blueprintQuestionLoaded, dispatch, metricGroupsInitiativeId, refetchMetricsUsage]
  );

  React.useEffect(() => {
    handleReload(true);
  }, [handleReload]);

  React.useEffect(() => {
    if (!isLoaded) {
      return;
    }

    if (
      groupId &&
      (Object.values(CustomMetricsViewMode).includes(groupId as CustomMetricsViewMode) ||
        metricGroups.find((g) => g._id === groupId))
    ) {
      return;
    }

    // Go to custom metric dashboard when doesn't have groudId
    return handleChangeMetricGroup({ groupId: CustomMetricsViewMode.Dashboard });
  }, [groupId, metricGroups, handleChangeMetricGroup, isLoaded]);

  if (!isLoaded || !initiativeId || !blueprintQuestionLoaded) {
    return <Loader />;
  }

  return (
    <CustomMetricContainer
      surveyId={surveyId ?? undefined}
      groupId={groupId}
      customMetricsUsage={customMetricsUsage}
      view={view}
      initiativeId={initiativeId}
      isPortfolioTracker={isPortfolioTracker}
      buttons={buttons}
      metricGroups={metricGroups}
      blueprintQuestions={blueprintQuestions}
    >
      <CustomMetricsComponent handleReload={handleReload} handleChangeMetricGroup={handleChangeMetricGroup} />
    </CustomMetricContainer>
  );
}

export default CustomMetrics;
