/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import './comments.scss';
import { UtrvStatus } from '@constants/status';
import { SurveyModelMinimalUtrv } from '@g17eco/types/survey';
import { checkHasComments } from '../survey/question/questionUtil';
import G17Client from '../../services/G17Client';
import { TextAreaWithAIDraft } from '@features/ai/TextAreaWithAiDraft';
import { AIRefineButton, HandleRefineData } from '@features/assistant/ai-assistant/AIRefineButton';
import { useGetAIUtrvFurtherNotesDraftRefineMutation } from '@api/ai';
import { QuestionReducerState } from '@g17eco/types/questionInterfaces';
import { QUESTION } from '@constants/terminology';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useAppSelector } from '../../reducers';
import { getRootConfig } from '../../selectors/globalData';
import { ValueHistory } from '../../types/universalTrackerValue';
import { Comment } from './Comment';
import { $createParagraphNode, $createTextNode, $getRoot, EditorState } from 'lexical';
import { useEffect } from 'react';
import { useGuardEditorState } from '@features/rich-text-editor';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { AI_MESSAGE } from '@constants/ai';

function canEdit({ isVerifier, isContributor }: { isVerifier: boolean; isContributor: boolean }, status: string) {
  if ([UtrvStatus.Verified, UtrvStatus.Rejected].includes(status as UtrvStatus)) {
    return isVerifier;
  }

  return isContributor;
}

interface CommentsInputProps {
  state: Pick<QuestionReducerState, 'table' | 'value' | 'valueData' | 'numberScale' | 'unit'>;
  disabled: boolean;
  comments: string;
  handleComments: (comments: string, editorState?: EditorState) => void;
  hideLabel: boolean;
  isVerifier: boolean;
  isContributor: boolean;
  utrv: SurveyModelMinimalUtrv;
  stakeholderHistory: Pick<ValueHistory, 'action' | 'note' | 'editorState'>;
  verifierHistory: Pick<ValueHistory, 'action' | 'note' | 'editorState'>;
  utrCode: string;
}

export const CommentsInput = (props: CommentsInputProps) => {
  const {
    state,
    disabled,
    comments,
    handleComments,
    hideLabel,
    isVerifier,
    isContributor,
    utrv,
    utrCode,
    stakeholderHistory,
    verifierHistory,
  } = props;

  const rootConfig = useAppSelector(getRootConfig);
  const canUseRichTextEditor = FeaturePermissions.canUseRichTextEditor(rootConfig);
  const canUseAIDraft = FeaturePermissions.canAccessAIDraftFurtherExplanation(rootConfig);
  const [getFurtherNotesDraftRefine] = useGetAIUtrvFurtherNotesDraftRefineMutation();

  const hasAction = (isVerifier || isContributor) && !disabled;
  const combinedHistory = [stakeholderHistory, verifierHistory];
  const hasComments = checkHasComments(stakeholderHistory, verifierHistory);

  const getDraftData = () => {
    if (state.table.rows.length > 0) {
      return {
        value: state.value,
        valueData: {
          // this is deprecated and should be removed in release/4.15 or later
          data: {
            table: state.table.rows,
          },
          table: state.table.rows.map((row) => row.data),
        },
      };
    }
    return {
      value: state.value,
      valueData: state.valueData,
      unit: state.unit,
      numberScale: state.numberScale,
    };
  };

  const initiativeId = utrv.initiativeId;

  const { editor, getGuardEditorState } = useGuardEditorState();

  useEffect(() => {
    editor.update(
      () => {
        const root = $getRoot();
        root.clear();
      },
      { onUpdate: () => editor.blur() },
    );
  }, [editor, utrv._id]);

  const handleEditOldNote = (history: Pick<ValueHistory, 'note' | 'editorState'>) => {
    if (history.editorState) {
      const guardEditorState = getGuardEditorState(history.editorState);
      editor.setEditorState(guardEditorState);
      handleComments(history.note ?? '', guardEditorState);

      return;
    }

    editor.update(
      () => {
        const root = $getRoot();
        root.clear();

        const paragraphNode = $createParagraphNode();
        paragraphNode.append($createTextNode(history.note));
        root.append(paragraphNode);
      },
      { onUpdate: () => editor.blur() },
    );

    handleComments(history.note ?? '', editor.getEditorState());
  };

  const onEditorAIWrite = (output: string) => {
    editor.update(() => {
      const root = $getRoot();
      root.clear();

      const paragraphNode = $createParagraphNode();
      paragraphNode.append($createTextNode(output));
      root.append(paragraphNode);
    });
  };

  return (
    <div className='comment-group'>
      {!hideLabel ? (
        <label className='my-1 mr-2'>
          <i className='fa fa-comment-alt mr-2' />
          Would you like to submit any further explanation?
        </label>
      ) : null}
      {hasAction ? (
        <TextAreaWithAIDraft
          key={`comments-textarea-ai-${utrv._id}`}
          name='comments'
          className='form-control commentBox'
          maxLength={5000}
          rows={6}
          canUseRichTextEditor={canUseRichTextEditor}
          onEditorAIWrite={onEditorAIWrite}
          onChange={handleComments}
          disabled={disabled}
          value={comments}
          getDraft={() => {
            return G17Client.getQuestionFurtherNotesDraft(initiativeId, {
              utrvId: utrv._id,
              draftData: getDraftData(),
            });
          }}
          renderButton={({ getDraft, setAppendString, setIsLoading, setIsReplacing }) => {
            return (
              <SimpleTooltip text={utrv.isPrivate ? AI_MESSAGE.EXCLUDED_PRIVATE_METRIC : ''}>
                <AIRefineButton
                  handleRefine={async (data: HandleRefineData) => {
                    // If there's no existing text, call the regular draft API
                    if (!comments?.trim()) {
                      return getDraft();
                    }

                    setIsLoading(true);

                    try {
                      const result = await getFurtherNotesDraftRefine({
                        initiativeId,
                        utrvId: utrv._id,
                        draftData: getDraftData(),
                        action: data.action,
                        additionalContext: data.additionalContext,
                        textToRefine: comments,
                      }).unwrap();

                      // Mark that we're replacing, not appending
                      setIsReplacing(true);
                      // Clear the current text
                      handleComments('');
                      // Trigger the typewriter animation with the refined content
                      setAppendString(result.content);
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                  utrCode={utrCode}
                  textContent={comments}
                  disabled={utrv.isPrivate}
                />
              </SimpleTooltip>
            );
          }}
          canUseAIDraft={canUseAIDraft}
        />
      ) : null}
      {combinedHistory.map((h, index) => {
        return h?.note ? (
          <Comment
            key={index ? 'verifier' : 'stakeholder'}
            history={h}
            handleComments={handleEditOldNote}
            canEdit={canEdit(props, h.action)}
            canUseRichTextEditor={canUseRichTextEditor}
          />
        ) : null;
      })}
      {!hasComments && !hasAction ? (
        <BasicAlert type='info'>There are no notes associated with this {QUESTION.SINGULAR}</BasicAlert>
      ) : null}
    </div>
  );
};
