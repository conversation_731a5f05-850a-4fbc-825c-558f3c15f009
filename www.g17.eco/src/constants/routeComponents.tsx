import { ROUTES } from './routes';
import { UserPreferencesRoute } from '../apps/common/pages/user/UserPreferencesRoute';
import { HydratedRouteInterface, HydratedRoutesInterface, RouteInterface, RoutesInterface } from '../types/routes';
import { ReportEditorCreateRoute } from '../apps/company-tracker/routes/report-editor/ReportEditorCreateRoute';
import { ReportEditorListRoute } from '../apps/company-tracker/routes/report-editor/ReportEditorListRoute';
import { ReportEditorViewRoute } from '../apps/company-tracker/routes/report-editor/ReportEditorViewRoute';
import { RedirectToCompanyTracker } from '../routes/company-tracker/RedirectToCompanyTracker';
import { ReportingRoute } from '../routes/company-tracker/ReportingRoute';
import { PortfolioNavigationMapRoute } from '../apps/portfolio-tracker/routes/PortfolioNavigationMapRoute';
import { PortfolioRoute } from '@apps/portfolio-tracker/components/portfolio';
import { CompanySponsorship } from '../routes/company-sponsorship';
import { CompanySettingsRoute as PortfolioCompanySettingsRoute } from '../apps/portfolio-tracker/routes/CompanySettingsRoute';
import { AdminSettingsRoute as PortfolioAdminSettingsRoute } from '../apps/portfolio-tracker/routes/AdminSettingsRoute';
import { PortfolioInsightRoute } from '../apps/portfolio-tracker/routes/PortfolioInsightRoute';
import { Benchmarking } from '@apps/portfolio-tracker/components/portfolio/benchmarking';
import { CustomMetricManagementRoute as PortfolioCustomMetricManagementRoute } from '../apps/portfolio-tracker/routes/CustomMetricManagementRoute';
import { CustomMetricManagementRoute as CompanyTrackerCustomMetricManagementRoute } from '../apps/company-tracker/routes/CustomMetricManagementRoute';
import { PortfolioTrackerCompanyRoute } from '../apps/portfolio-tracker/routes/PortfolioTrackerCompanyRoute';
import { UserManagementRoute as PortfolioUserManagementRoute } from '../apps/portfolio-tracker/routes/UserManagementRoute';
import { CompanyManagement } from '../routes/company-management';
import UserProfile from '../routes/user-profile';
import { UniversalTrackerProvenance } from '../routes/universal-tracker-provenance';
import { PublicSDGContributionChart } from '../routes/public-sdg-contribution-chart';
import { SharedDashboard } from '../routes/custom-dashboard/shared-dashboard/SharedDashboard';
import { Company as StatsCompany } from '@features/stats/reports/company';
import { StatsSurvey } from '@features/stats/reports/survey';
import { StatsScope } from '@features/stats/reports/scope';
import { StatsStandards } from '@features/stats/reports/standards';
import { StatsQuestions } from '@features/stats/reports/questions';
import { CarbonCalculatorComparisonRoute } from '../apps/carbon-calculator/components/carbon-calculator-comparison';
import { SimpleCarbonCalculator } from '../apps/carbon-calculator/components/simple-carbon-calculator';
import { PartnerRoute } from '../apps/carbon-calculator/routes/PartnerRoute';
import { Taxonomy } from '../routes/sdgap/Taxonomy';
import { RedirectToMaterialityTracker } from '../apps/materiality-tracker/routes/RedirectToMaterialityTracker';
import { CompanySettingsRoute as MaterialityCompanySettingsRoute } from '../apps/materiality-tracker/routes/CompanySettingsRoute';
import { AssessmentInsightRoute } from '../apps/materiality-tracker/routes/assessment/AssessmentInsightRoute';
import { UserManagementRoute as MaterialityUserManagementRoute } from '../apps/materiality-tracker/routes/UserManagementRoute';
import { AdminSettingsRoute as MaterialityAdminSettingsRoute } from '../apps/materiality-tracker/routes/AdminSettingsRoute';
import { CustomMetricGroupRoute as MaterialityCustomMetricGroupRoute } from '../apps/materiality-tracker/routes/CustomMetricGroupRoute';
import { AssessmentListRoute } from '../apps/materiality-tracker/routes/assessment/AssessmentListRoute';
import { AssessmentRoute } from '../apps/materiality-tracker/routes/assessment/AssessmentRoute';
import { BankingSettingsRoute } from '../apps/materiality-tracker/routes/BankingSettingsRoute';
import { StockExchangeSettingsRoute as MaterialityStockExchangeSettingsRoute } from '../apps/materiality-tracker/routes/StockExchangeSettingsRoute';
import { IntegrationsRoute } from '../apps/materiality-tracker/routes/IntegrationsRoute';
import { CustomReportsRoute } from '@features/downloads/CustomReportsRoute';
import { AssurerAssurancePortfolio } from '@apps/assurance/components/assurer-assurance-portfolio/assurer-assurance-portfolio';
import { UserManagementRoute as AssuranceUserManagementRoute } from '../apps/assurance/routes/UserManagementRoute';
import { InitiativeStructureContainer } from '../routes/initiative-structure/InitiativeStructureContainer';
import { InsightRoute } from '../routes/company-tracker/InsightRoute';
import { PPTXReportsRoute } from '../apps/company-tracker/components/downloads/pptx/PPTXReportsRoute';
import { DownloadsRoute } from '@features/downloads/DownloadsRoute';
import { ManageWorkgroupsRoute } from '../apps/company-tracker/components/manage-workgroups';
import { UserManagementRoute as CompanyTrackerUserManagementRoute } from '../apps/company-tracker/routes/UserManagementRoute';
import { EmissionsCalculatorIntegrationRoute } from '../apps/company-tracker/routes/integrations/EmissionsCalculatorIntegrationRoute';
import { AppIntegrationViewRoute } from '../apps/company-tracker/routes/integrations/AppIntegrationViewRoute';
import { CompanyTrackerIntegrationsRoute } from '../apps/company-tracker/routes/integrations/CompanyTrackerIntegrationsRoute';
import { DataIntegrationsRoute } from '../apps/company-tracker/routes/data-integrations/DataIntegrationsRoute';
import { DataShareRoute } from '../routes/data-share/DataShareRoute';
import { ReferralsRoute } from '../routes/referrals/ReferralsRoute';
import { AdminDashboardRoute } from '../apps/company-tracker/routes/admin-dashboard/AdminDashboardRoute';
import { SystemLog } from '../apps/company-tracker/components/system-log';
import { SubsidiaryUserDelegation } from '../apps/company-tracker/components/admin-dashboard/subsidiary-user-delegation';
import { CompanySettingsRoute as CompanyTrackerCompanySettingsRoute} from '../apps/company-tracker/routes/CompanySettingsRoute';
import { BankingSettingsRoute as CompanyTrackerBankingSettingsRoute } from '../apps/company-tracker/routes/BankingSettingsRoute';
import { StockExchangeSettingsRoute as CompanyTrackerStockExchangeSettingsRoute } from '../apps/company-tracker/routes/StockExchangeSettingsRoute';
import { DocumentLibraryRoute } from '../apps/company-tracker/components/document-library/DocumentLibraryRoute';
import { AdminSettingsRoute as CompanyTrackerAdminSettingsRoute } from '../apps/company-tracker/routes/AdminSettingsRoute';
import { BulkImporting } from '../apps/company-tracker/components/bulk-importing';
import { SurveyTemplatesRoute } from '../routes/survey-template';
import { PrimaryConnections } from '../routes/sdgap/PrimaryConnections';
import { ReportSettingsRoute } from '../apps/company-tracker/routes/ReportSettingsRoute';

const ROUTE_COMPONENTS: { [K in keyof typeof ROUTES]: HydratedRouteInterface['component'] } = {
  USER_PREFERENCES: UserPreferencesRoute,
  USER_PROFILE: UserProfile,
  UTRV_PROVENANCE: UniversalTrackerProvenance,
  PUBLIC_SDG_CHART: PublicSDGContributionChart,
  SHARED_CUSTOM_DASHBOARD: SharedDashboard,
  COMPANY_TRACKER_SURVEY_REDIRECTOR: RedirectToCompanyTracker,
  COMPANY_TRACKER_LIST: ReportingRoute,
  COMPANY_TRACKER_ASSURANCE: ReportingRoute,
  COMPANY_TRACKER_SURVEY: ReportingRoute,

  NAVIGATE_BY_MAP: InitiativeStructureContainer,
  NAVIGATE_BY_ARCHIVED: InitiativeStructureContainer,
  SUMMARY_CURRENT: InsightRoute,
  CUSTOM_DASHBOARD: InsightRoute,
  SUMMARY: InsightRoute,
  DOWNLOADS_CUSTOM: CustomReportsRoute,
  DOWNLOADS_PPTX: PPTXReportsRoute,
  DOWNLOADS: DownloadsRoute,
  MANAGE_WORKGROUPS: ManageWorkgroupsRoute,
  MANAGE_USERS: CompanyTrackerUserManagementRoute,
  EMISSIONS_CALCULATOR_INTEGRATIONS: EmissionsCalculatorIntegrationRoute,
  APP_INTEGRATIONS_VIEW: AppIntegrationViewRoute,
  APP_INTEGRATIONS: CompanyTrackerIntegrationsRoute,
  DATA_INTEGRATIONS: DataIntegrationsRoute,
  REPORT_EDITOR_CREATE: ReportEditorCreateRoute,
  REPORT_EDITOR_LIST: ReportEditorListRoute,
  REPORT_EDITOR_VIEW: ReportEditorViewRoute,
  REPORT_SETTINGS: ReportSettingsRoute,
  CUSTOM_METRICS: CompanyTrackerCustomMetricManagementRoute,
  DATA_SHARE_INITIATIVE: DataShareRoute,
  REFERRALS: ReferralsRoute,
  ADMIN_DASHBOARD: AdminDashboardRoute,
  SYSTEM_LOG: SystemLog,
  SUBSIDIARY_USER_DELEGATION: SubsidiaryUserDelegation,
  ACCOUNT_SETTINGS: CompanyTrackerCompanySettingsRoute,
  BANKING_SETTINGS: CompanyTrackerBankingSettingsRoute,
  STOCK_EXCHANGE_SETTINGS: CompanyTrackerStockExchangeSettingsRoute,
  DOCUMENT_LIBRARY: DocumentLibraryRoute,
  ADMIN_SETTINGS: CompanyTrackerAdminSettingsRoute,
  BULK_IMPORTING: BulkImporting,
  SURVEY_TEMPLATES: SurveyTemplatesRoute,
  SURVEY_TEMPLATES_VIEW: SurveyTemplatesRoute,
  SURVEY_TEMPLATES_HISTORY: SurveyTemplatesRoute,

  EMISSIONS_CALCULATORS: CarbonCalculatorComparisonRoute,
  EMISSIONS_PARTNER: PartnerRoute,
  SIMPLE_CARBON_CALCULATOR: SimpleCarbonCalculator,

  PORTFOLIO_TRACKER_MAP: PortfolioNavigationMapRoute,
  PORTFOLIO_TRACKER_PORTFOLIO: PortfolioRoute,
  PORTFOLIO_TRACKER_MANAGE_SPONSORSHIPS: CompanySponsorship,
  PORTFOLIO_TRACKER_COMPANY_SETTINGS: PortfolioCompanySettingsRoute,
  PORTFOLIO_TRACKER_ADMIN_SETTINGS: PortfolioAdminSettingsRoute,
  PORTFOLIO_TRACKER_INSIGHTS: PortfolioInsightRoute,
  PORTFOLIO_TRACKER_INSIGHT_DASHBOARDS: PortfolioInsightRoute,
  PORTFOLIO_TRACKER_BENCHMARKING: Benchmarking,
  PORTFOLIO_TRACKER_CUSTOM_METRICS: PortfolioCustomMetricManagementRoute,
  PORTFOLIO_TRACKER_COMPANY: PortfolioTrackerCompanyRoute,
  PORTFOLIO_TRACKER_COMPANY_DOWNLOADS: PortfolioTrackerCompanyRoute,
  PORTFOLIO_TRACKER_COMPANY_DASHBOARD: PortfolioTrackerCompanyRoute,
  PORTFOLIO_TRACKER_MANAGE_USERS: PortfolioUserManagementRoute,
  PORTFOLIO_TRACKER_MANAGE_COMPANIES: CompanyManagement,
  PORTFOLIO_TRACKER_SURVEY_OVERVIEW: PortfolioTrackerCompanyRoute,

  ASSURANCE_PORTFOLIO: AssurerAssurancePortfolio,
  ASSURANCE_MANAGE_USERS: AssuranceUserManagementRoute,

  MATERIALITY_TRACKER_REDIRECT: RedirectToMaterialityTracker,
  MATERIALITY_TRACKER_COMPANY_SETTINGS: MaterialityCompanySettingsRoute,
  MATERIALITY_TRACKER_MANAGE_USERS: MaterialityUserManagementRoute,
  MATERIALITY_TRACKER_ADMIN: MaterialityAdminSettingsRoute,
  MATERIALITY_TRACKER_INSIGHTS: AssessmentInsightRoute,
  MATERIALITY_TRACKER_MODULES: MaterialityCustomMetricGroupRoute,
  MATERIALITY_TRACKER_LIST: AssessmentListRoute,
  MATERIALITY_TRACKER_QUESTION_INDEX: AssessmentRoute,
  MATERIALITY_TRACKER_QUESTION: AssessmentRoute,
  MATERIALITY_TRACKER_SURVEY: AssessmentRoute,
  MATERIALITY_TRACKER_BANKING_SETTINGS: BankingSettingsRoute,
  MATERIALITY_TRACKER_STOCK_EXCHANGE_SETTINGS: MaterialityStockExchangeSettingsRoute,
  MATERIALITY_TRACKER_INTEGRATIONS: IntegrationsRoute,

  SDGAP: Taxonomy,
  SDGAP_PRIMARY_CONNECTIONS: PrimaryConnections,

  STATS_COMPANIES: StatsCompany,
  STATS_SURVEY: StatsSurvey,
  STATS_SCOPE: StatsScope,
  STATS_STANDARDS: StatsStandards,
  STATS_QUESTIONS: StatsQuestions,
} as const;

const getRouteComponent = (componentKey: keyof typeof ROUTES): HydratedRouteInterface['component'] => {
  return ROUTE_COMPONENTS[componentKey];
};

const getHydratedRoute = (route: RouteInterface, componentKey: keyof typeof ROUTES): HydratedRouteInterface => {
  return {
    ...route,
    component: getRouteComponent(componentKey),
  };
};

export const getHydratedRoutes = (routes: RoutesInterface): HydratedRoutesInterface => {
  return Object.keys(routes).reduce((acc, componentKey) => {
    acc[componentKey] = getHydratedRoute(routes[componentKey], componentKey);
    return acc;
  }, {} as HydratedRoutesInterface);
};
