/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { BulkActionUtrMin, UniversalTrackerPlain, ValueValidation } from './universalTracker';
import { CustomScope, InitiativeMinPlain, InitiativePlain } from './initiative';
import { Group, Standards } from '@g17eco/core';
import { TransformMapping } from '../utils/file/columnMapping';
import { UtrvFilter } from './insight-custom-dashboard-filter';
import { DataPeriods } from '@utils/dataPeriods';
import { UtrvUserRole } from '@constants/user';
import { OrderingDirection } from './common';
import { DelegationScope } from './surveyDelegation';
import { UniversalTrackerValuePlain } from './universalTrackerValue';
import { SerializedEditorState } from 'lexical';
import { StakeholderGroup } from '../model/stakeholderGroup';
import UniversalTrackerClassInterface from '../model/UniversalTrackerClassInterface';
import { SortableItem } from '../utils/sort';
import { SurveyScope, ScopeGroups, SurveyRoles, SurveyType } from './surveyCommon';
import { SurveyPermission } from './permission';
import { CardGridViewMode } from '../features/survey-scope/CardGrid';

export interface ScopeQuestion extends ScopeQuestionOptionalValue {
  utrv: SurveyModelMinimalUtrv,
}

export interface BaseScopeQuestion {
  utrv?: SurveyModelMinimalUtrv,
  name: string,
  universalTracker: UniversalTrackerClassInterface,
  frameworkCode: string,
  valueType: string,
  valueValidation: ValueValidation,
}

export interface ScopeQuestionOptionalValue extends BaseScopeQuestion, SortableItem {
  shortPrefix: string;
}

export enum AggregationMode {
  Children = 'children', // Used for tree aggregations up to parent
  Combined = 'combined', // Used for combined reports (e.g. monthly reports to a combined yearly)
}

export enum CustomMetricOrderType {
  Name = 'name',
  TypeCode = 'typeCode',
  Custom = 'custom',
}

export interface CustomMetricsOrder {
  orderType: CustomMetricOrderType;
  // Used for non-custom order
  direction?: OrderingDirection;
}

export type SurveyListItem = Pick<SurveyModelMinData,
  | '_id'
  | 'name'
  | 'scope'
  | 'completedDate'
  | 'type'
  | 'period'
  | 'effectiveDate'
>

export interface UnitConfig {
  area: string;
  length: string;
  time: string;
  mass: string;
  volume: string;
  energy: string;
  currency: string;
  co2Emissions: string;
  numberScale: string;
  partsPer: string;
}

export interface SurveyInitiative {
  _id: string;
  initiativeId: string;
  initiative: InitiativeMinPlain;
  effectiveDate: string;
  name?: string;
  completedDate?: string;
  aggregatedDate?: string;
  aggregatedVersion?: number;
  aggregatedVersionMismatch?: boolean;
  period?: DataPeriods;
  type?: SurveyType;
  sourceName?: string;
  unitConfig?: UnitConfig;
}

export interface CSVFormat {
  QuestionCode: string;
  Question?: string;
  Value: string;
  Comment?: string;
  OptionCode?: string;
}

export interface SurveyAddAssurance {
  _id: string;
  initiativeId: string;
}

export interface MassDelegation {
  roles: UtrvUserRole[];
  utrvIds: string[];
  userIds: string[];
}

export interface BlueprintContributions {
  [k: string]: string[];
}

export interface QuestionList<T extends ScopeQuestionOptionalValue = ScopeQuestion> extends Pick<Group, 'code' | 'name'>{
  list: T[];
}

export interface QuestionSubGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> extends QuestionList<T> {
  subGroups?: QuestionSubGroup<T>[]; // 2nd level of subgroups
}

export interface BaseQuestionGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> {
  alwaysVisible?: boolean;
  groupName: string;
  groupData?: GroupData;
  groupCode?: string;
  groupId?: string;
  count?: number;
  list: T[];
  subGroups?: QuestionSubGroup<T>[]; // 1st level of subgroups
}

interface SurveyQuestionBase {
  utr: UniversalTrackerPlain;
}

export interface QuestionGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> extends BaseQuestionGroup<T> {
  questions: SurveyQuestionBase[],
}

// Alias
export type ScopeQuestionGroupOptionalValue = BaseQuestionGroup<ScopeQuestionOptionalValue>;
export type ScopeQuestionGroup = BaseQuestionGroup<ScopeQuestion>;

export interface ScopeGroupData {
  title: string;
  subtitle?: string;
  description?: string;
  src?: string;
  link?: string;
  icon?: string;
  colour?: string;
  preferredAltCodes?: string[];
  // Used for sorting utrs in metric groups
  metricsOrder?: CustomMetricsOrder;
  universalTrackerIds?: string[];
  isInherited?: boolean;
}

export interface ScopeGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> {
  group: ScopeGroupData;
  list: T[];
}

export interface SurveySettings {
  groupBy: string[];
  viewLayout: CardGridViewMode;
  filterByStatus: string[];
  filterByDelegationStatus: string[];
  filterByGoal: string[];
  filterByRegulatory: string[];
  filterByModules: string[];
  filterByRole: string[];
  filterByUser: string[];
  filterByTag: string[];
  searchText: string;
  filterByBookmarks: boolean;
}

export interface SurveyScopeFilters {
  filterByMateriality: string[];
}

export type UpdateScopeFiltersType = (key: keyof SurveyScopeFilters, setting: string[] | boolean | string) => void;

export type ScopeCategoryGroups<T extends ScopeQuestionOptionalValue = ScopeQuestion> = {
  [key in ScopeGroups]: {
    [key: keyof Standards]: ScopeGroup<T>
  }
};

export interface SurveyImport {
  file: File;
  mapping: TransformMapping<string>;
  tabName: string;
}
export interface BulkSurveyImport {
  initiativeId: string;
  file: File;
}

export type AllowedAggregatedUtrvStatus = UtrvFilter.AllAnswered | UtrvFilter.Verified;
export interface AggregatedSurveyFilters {
  utrv: AllowedAggregatedUtrvStatus | undefined;
}

export interface CreateCombineSurvey {
  name: string,
  surveyIds: string[];
  period?: DataPeriods;
  scope?: SurveyScope;
  aggregationMode?: AggregationMode;
  filters: AggregatedSurveyFilters;
}

export enum AssessmentType {
  FinancialMateriality = 'financial_materiality',
  DoubleMateriality = 'double_materiality',
}

export interface ScopeWheelPreferences {
  scopeCode: string;
  name: string;
  visible: boolean;
}

export enum GroupType {
  Custom = 'custom',
  Group = 'group',
  Static = 'static',
}

export interface UtrGroupConfig {
  utrCodes: string[];
  groupName: string;
  groupData: GroupData;
  type?: GroupType;
  groupId?: string;
  groupDate?: string;
}

export interface NonStaticGroupConfig extends UtrGroupConfig {
  groupId: string;
  type: GroupType;
}

export interface Form {
  utrGroupConfig: UtrGroupConfig;
}

export interface AdditionalConfig {
  compositeConfig: string;
}

// specified also on api unitTypes.ts
export const blueprintDefaultUnitConfig: UnitConfig = {
  area: 'km2',
  length: 'km',
  time: 'h',
  mass: 'mt',
  volume: 'm3',
  energy: 'MWh',
  currency: 'USD',
  co2Emissions: 'tons/CO2e',
  numberScale: 'millions',
  partsPer: 'ppm',
};

export const blueprintDefaultUnitConfigSgx: UnitConfig = {
  ...blueprintDefaultUnitConfig,
  currency: 'SGD'
}

export interface Blueprint {
  references: any[];
  forms: Form[];
  additionalConfigs: AdditionalConfig[];
  code: string;
  name: string;
  unitConfig: UnitConfig;
  customGroups?: NonStaticGroupConfig[];
}

interface ScopeUpdate {
  _id?: string;
  type: GroupType;
  name: string;
  added: string[],
  removed: string[],
}

type SurveyModelMinimalInitiative = Pick<
  InitiativePlain,
  '_id' | 'name' | 'customer' | 'referrals' | 'permissionGroup' | 'type' | 'tags' | 'materialityMap'
>;

export interface ScheduledDate {
  idempotencyKey?: string;
  date: string;
}

export interface FormScheduledDate extends ScheduledDate {
  status: 'updated' | 'original';
}

export type NoteBaseTypes = Pick<SurveyModelMinData,
  | 'noteInstructions'
  | 'noteInstructionsEditorState'
  | 'noteRequired'
>

export interface Company {
  permissionGroup?: string;
}

export interface UtrSortingConfig {
  code: string;
  order?: string[];
  subgroups?: {
    code: string;
    order?: string[];
    subgroups?: UtrSortingConfig['subgroups'];
  }[];
}

interface UtrSortingConfigMap {
  [key: string]: UtrSortingConfig | undefined;
}

export interface GroupData {
  colour?: string;
  link?: string;
  icon?: string;
  alternativeCode?: string;
  preferredAltCodes?: string[];
  isInherited?: boolean;
}

export interface SurveyModelMinData {
  _id: string;
  code: string;
  initiativeId: string;
  effectiveDate: string;
  completedDate?: string;
  utrvType: string;
  evidenceRequired: boolean;
  verificationRequired: boolean;
  noteRequired?: boolean;
  noteInstructions?: string;
  noteInstructionsEditorState?: SerializedEditorState;
  isPrivate: boolean;
  stakeholders: StakeholderGroup;
  name?: string;
  type?: SurveyType;
  sourceName: string;
  scope?: SurveyScope;
  scopeConfig?: CustomScope[];
  delegationScope?: DelegationScope;
  roles?: SurveyRoles;
  permissions?: SurveyPermission[];
  config?: Pick<Blueprint, 'unitConfig' | 'customGroups' | 'forms'>;
  unitConfig?: UnitConfig;
  period?: DataPeriods;
  created: string;
  initiatives: SurveyModelMinimalInitiative[];
  scopeUpdates?: ScopeUpdate[];
  ignoredDate: string;
  deadlineDate?: string;
  scheduledDates?: ScheduledDate[];
  creatorId?: string;
  utrSortingConfigMap?: UtrSortingConfigMap;
}

export type SurveyModelMinimalUtrv = Pick<
  UniversalTrackerValuePlain,
  | '_id'
  | 'universalTrackerId'
  | 'initiativeId'
  | 'valueData'
  | 'value'
  | 'status'
  | 'stakeholders'
  | 'assuranceStatus'
  | 'lastUpdated'
  | 'evidenceRequired'
  | 'noteRequired'
  | 'verificationRequired'
  | 'isPrivate'
  | 'effectiveDate'
  | 'compositeData'
  | 'notes'
  | 'sourceItems'
> & { utr?: BulkActionUtrMin };
