import { MetricGroup } from './metricGroup';
import { SurveyWorkgroup } from './workgroup';
import {
  AggregatedSurveyFilters,
  BlueprintContributions,
  QuestionGroup,
  ScopeWheelPreferences,
  SurveyModelMinData,
  SurveyModelMinimalUtrv,
} from './survey';

interface DisplayPreferences {
  scopeWheels?: ScopeWheelPreferences[];
}
export interface SurveyActionData extends SurveyModelMinData {
  fragmentUniversalTrackerValues: SurveyModelMinimalUtrv[];
  questionGroups: QuestionGroup[];
  customMetricGroups: MetricGroup[];
  contributions: BlueprintContributions;
  displayPreferences?: DisplayPreferences;
  aggregatedDate?: string;
  aggregatedSurveys?: Pick<SurveyModelMinData, '_id' | 'initiativeId' | 'effectiveDate' | 'type' | 'period'>[];
  filters?: AggregatedSurveyFilters;
  workgroups: SurveyWorkgroup[];
}
