import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { AIDisclaimer } from './index';

describe('AIDisclaimer', () => {
  it('should render the AI disclaimer component', () => {
    render(<AIDisclaimer className='custom-class' />);

    const disclaimerContainer = screen.getByText(/AI generated drafts/).closest('div');
    const disclaimerText = screen.getByText(/AI generated drafts are for guidance only/);

    expect(disclaimerText).toBeInTheDocument();

    // display the sparkles icon
    const sparklesIcon = disclaimerContainer?.querySelector('i.fal.fa-sparkles');
    expect(sparklesIcon).toBeInTheDocument();

    // display the main disclaimer text about AI drafts
    const fullText = disclaimerText.textContent;
    expect(fullText).toBe(
      'AI generated drafts are for guidance only and should be carefully checked and edited by a human to ensure they are factual.',
    );
  });
});
