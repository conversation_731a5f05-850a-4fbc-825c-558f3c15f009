import { faker } from '@faker-js/faker';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { BaseQuestionGroup, ScopeQuestion } from '@g17eco/types/survey';
import { SurveyOverviewMode } from '@g17eco/types/surveyOverview';
import { describe, it } from 'vitest';
import { createMetricGroup } from '../__fixtures__/metric-group-fixtures';
import { createUtr, createUtrv } from '../__fixtures__/utr/utrv-factory';
import { SurveyGroupsData } from '../model/surveyData';
import UniversalTracker from '../model/UniversalTracker';
import { getSurveyGroups } from './survey';

describe('Survey utils', () => {
  describe('getSurveyGroups', () => {
    const blueprintGroups: BaseQuestionGroup[] = [];

    describe('getSurveyGroupsByScopeCategories', () => {
      const utrs = Array.from({ length: 3 }).map((_, i) => createUtr(`custom/code/${i}`, { name: `Question ${i}` }));
      const [utr11, utr12, utr2] = utrs;

      const subGroup11 = createMetricGroup({ groupName: 'Child Group 1.1', universalTrackers: [utr11._id] });
      const subGroup12 = createMetricGroup({ groupName: 'Child Group 1.2', universalTrackers: [utr12._id] });
      const group1 = createMetricGroup({
        groupName: 'Metric Group 1',
        // metricsOrder is not set, so it should sort questions by name
        universalTrackers: [utr11._id, utr12._id],
        subgroups: [subGroup11, subGroup12],
      });

      const group2 = createMetricGroup({
        groupName: 'Metric Group 2',
        universalTrackers: [utr2._id],
      });

      const createQuestionGroup = (metricGroup: MetricGroup) => ({
        groupId: metricGroup._id,
        groupName: metricGroup.groupName,
        groupData: metricGroup.groupData,
        questions: utrs.filter((utr) => metricGroup.universalTrackers?.includes(utr._id)).map((utr) => ({ utr })),
        list: [],
      });

      const questionGroup1 = createQuestionGroup(group1);
      const questionGroup11 = createQuestionGroup(subGroup11);
      const questionGroup12 = createQuestionGroup(subGroup12);
      const questionGroup2 = createQuestionGroup(group2);

      const createSurveyData = (overrides: Partial<SurveyGroupsData> = {}): SurveyGroupsData => ({
        initiativeId: faker.database.mongodbObjectId(),
        initiatives: [{ _id: faker.database.mongodbObjectId(), name: 'Initiative Name' }],
        contributions: {},
        customMetricGroups: [],
        questionGroups: [],
        scope: {
          standards: [],
          frameworks: [],
          sdg: [],
          materiality: [],
          custom: [],
        },
        ...overrides,
      });

      const scopeQuestions: ScopeQuestion[] = utrs.map((utr) => ({
        name: utr.name,
        shortPrefix: '',
        universalTracker: new UniversalTracker(utr),
        frameworkCode: utr.typeCode,
        valueType: utr.valueType,
        valueValidation: {},
        utrv: createUtrv(utr._id),
      }));

      const [question11, question12, question2] = scopeQuestions;

      describe('only metric groups added to survey', () => {
        it('full metric groups with its subgroups in scope', () => {
          const surveyData = createSurveyData({
            customMetricGroups: [group1, group2],
            questionGroups: [questionGroup1, questionGroup11, questionGroup12, questionGroup2],
            scope: {
              standards: [],
              frameworks: [],
              sdg: [],
              materiality: [],
              custom: [group1._id, subGroup11._id, subGroup12._id, group2._id],
            },
          });

          const result = getSurveyGroups(SurveyOverviewMode.ScopeGroups, surveyData, blueprintGroups, scopeQuestions);

          const [_, group1Result, group2Result] = result;
          expect(group1Result.groupName).toEqual('Metric Group 1');
          expect(group1Result.list).toEqual([question11, question12]);

          expect(group1Result.subGroups).toEqual([
            {
              code: subGroup11._id,
              name: 'Child Group 1.1',
              list: [question11],
              subGroups: undefined,
            },
            {
              code: subGroup12._id,
              name: 'Child Group 1.2',
              list: [question12],
              subGroups: undefined,
            },
          ]);
          expect(group2Result.groupName).toEqual('Metric Group 2');
          expect(group2Result.list).toEqual([question2]);
          expect(group2Result.subGroups).toBeUndefined();
        });

        it('full metric groups without its subgroups in scope', () => {
          const surveyData = createSurveyData({
            customMetricGroups: [group1, group2],
            questionGroups: [questionGroup1, questionGroup2],
            scope: {
              standards: [],
              frameworks: [],
              sdg: [],
              materiality: [],
              custom: [group1._id, group2._id], // no subgroups
            },
          });

          const result = getSurveyGroups(SurveyOverviewMode.ScopeGroups, surveyData, blueprintGroups, scopeQuestions);

          const [_, group1Result, group2Result] = result;
          expect(group1Result.groupName).toEqual('Metric Group 1');
          expect(group1Result.list).toEqual([question11, question12]);

          expect(group1Result.subGroups).toEqual([
            {
              code: subGroup11._id,
              name: 'Child Group 1.1',
              list: [question11],
              subGroups: undefined,
            },
            {
              code: subGroup12._id,
              name: 'Child Group 1.2',
              list: [question12],
              subGroups: undefined,
            },
          ]);
          expect(group2Result.groupName).toEqual('Metric Group 2');
          expect(group2Result.list).toEqual([question2]);
          expect(group2Result.subGroups).toBeUndefined();
        });

        it('partial metric groups with its subgroups in scope', () => {
          const surveyData = createSurveyData({
            customMetricGroups: [group1, group2],
            questionGroups: [questionGroup11],
            scope: {
              standards: [],
              frameworks: [],
              sdg: [],
              materiality: [],
              custom: [subGroup11._id], // no parent
            },
          });
          const result = getSurveyGroups(SurveyOverviewMode.ScopeGroups, surveyData, blueprintGroups, scopeQuestions);

          const [_, group1Result, group2Result] = result;
          
          expect(group1Result.groupName).toEqual('Metric Group 1');
          expect(group1Result.list).toEqual([question11]);

          expect(group1Result.subGroups).toEqual([
            {
              code: subGroup11._id,
              name: 'Child Group 1.1',
              list: [question11],
              subGroups: undefined,
            },
            {
              code: subGroup12._id,
              name: 'Child Group 1.2',
              list: [], // no questions
              subGroups: undefined,
            },
          ]);

          expect(group2Result).toBeUndefined();
        });
      });
    });
  });
});
