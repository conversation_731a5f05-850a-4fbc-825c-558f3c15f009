/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */


import * as <PERSON><PERSON> from '@sentry/react';
import config from './config'

interface SentryUser {
  id: string;
  email?: string;
  username?: string;

  organization?: {
    id: string;
    name: string;
    appConfigCode?: string;
  };
}

export const setSentryUser = (user: SentryUser | null) => {
  Sentry.setUser(user);
}

export const showReportDialog = () => Sentry.showReportDialog();

export const init = () => {
  if (!config.sentry.enabled) {
    return;
  }
  return Sentry.init({
    dsn: config.sentry.dsn,
    enabled: config.sentry.enabled,
    environment: config.appEnv,
    integrations: [Sentry.browserTracingIntegration()],

    // Set tracesSampleRate to 1.0 to capture 100%
    // of transactions for performance monitoring.
    // We recommend adjusting this value in production
    tracesSampleRate: config.sentry.tracesSampleRate || undefined,
    // Set `tracePropagationTargets` to control for which URLs distributed tracing should be enabled
    tracePropagationTargets: ['localhost', /^https:\/\/api(\.(.*))?\.g17\.eco\/api/],
    sendDefaultPii: config.sentry.sendDefaultPii,
  });
};

export const handleRouteError = (error: Error, errorInfo?: Record<string, unknown>) => {
  if (!config.isProd) {
    return console.error(error, errorInfo);
  }

  Sentry.withScope(scope => {
    if (typeof errorInfo === 'object') {
      Object.keys(errorInfo).forEach(key => scope.setExtra(key, errorInfo[key]));
    }
    Sentry.captureException(error);
  });
};

type Level = 'error' | 'info';
export const loggerMessage = (msg: string, context?: { level?: Level, [k: string]: unknown }) => {
  if (!config.isProd) {
    return console.warn(msg, context);
  }

  // If the level is info, and it's not the minimum log level, don't log it
  if (context?.level === 'info' && context.level !== config.minLogLevel) {
    return;
  }

  Sentry.captureMessage(msg, (scope) => {
    if (typeof context === 'object') {
      const { level, ...rest } = context;

      if (level) {
        scope.setLevel(level)
      }

      Object.keys(rest).forEach(key => scope.setExtra(key, context[key]));
    }
    return scope
  })
}
