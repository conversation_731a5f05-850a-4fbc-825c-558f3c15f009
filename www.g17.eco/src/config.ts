/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

const debug = Boolean(import.meta.env.REACT_APP_DEBUG || false);
const appEnv = import.meta.env.MODE || 'development';

const config = {
  appEnv: appEnv,
  isK8s: <PERSON><PERSON><PERSON>(import.meta.env.REACT_APP_K8S),
  isUserLocationEnabled: <PERSON><PERSON>an(import.meta.env.REACT_APP_USER_LOCATION_ENABLED),
  isProd: import.meta.env.PROD,
  apiUrl: import.meta.env.REACT_APP_API_SERVER + '/api/',
  websocketUrl: import.meta.env.REACT_APP_WEBSOCKET_URL,
  appBrandingEnv: import.meta.env.REACT_APP_BRANDING_ENV,
  graphCMSURI:
    import.meta.env.REACT_APP_GRAPHCMS_URI ||
    'https://eu-central-1.cdn.hygraph.com/content/ckdsvoei8740d01z1aafx6gak/master',
  graphCMSToken:
    import.meta.env.REACT_APP_GRAPHCMS_TOKEN ||
    '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  graphCMSLegalId: import.meta.env.REACT_APP_GRAPHCMS_LEGAL_ID || 'ckf49yybs01vi0104tfk86ibi',
  magicBellApiKey: import.meta.env.REACT_APP_MAGICBELL_API_KEY || '784344a7c9af567c9548a59ed2b35156efbb56d1',
  magicBellNotificationURL: 'https://api.magicbell.com/notifications/',
  // registerLink: 'https://www.worldwidegeneration.co/register-interest/1',
  // emailWwg: import.meta.env.EMAIL_WWG || '<EMAIL>',
  emailSupport: import.meta.env.REACT_APP_EMAIL_SUPPORT || '<EMAIL>',
  // phoneSupport: import.meta.env.REACT_APP_PHONE_SUPPORT || '+44 ************',
  features: {
    draftMode: Boolean(import.meta.env.REACT_APP_DRAFT_MODE_ENABLED),
    csrdDownload: Boolean(import.meta.env.REACT_APP_CSRD_DOWNLOAD_ENABLED),
  },
  googleMapsKey: import.meta.env.REACT_APP_GOOGLE_API_KEY || 'AIzaSyDSZsLNUvjIO_5WKhVtDwPzT2d88_dvdYc',
  upgradeCompanyTrackerLink: 'https://calendly.com/wwg-cs/company-tracker-light-upgrades',
  userLocationApiKey: '9d21a93c1d7c4194a588d50cc2925011',
  analytics: {
    app: 'G17Eco',
    version: appEnv,
    enabled: !import.meta.env.REACT_APP_DISABLE_ANALYTICS,
    debug: debug,
    segment: {
      writeKey: import.meta.env.REACT_APP_SEGMENT_WRITE_KEY || 'dijBBeOrEkeTJJsBlShjw8DX5O6uviQM',
      disableAnonymousTraffic: false,
    },
    googleAnalytics: {
      debug: debug,
      measurementId: import.meta.env.REACT_APP_GA_MEASUREMENT_ID || 'G-03PSHLB60C',
    },
    googleTagManager: {
      containerId: import.meta.env.REACT_APP_GTM_CONTAINER_ID || 'GTM-W2KWJ7B',
    },
    hotjar: {
      id: import.meta.env.REACT_APP_HOTJAR_ID,
      snippetVersion: import.meta.env.REACT_APP_HOTJAR_SNIPPET_VERSION || 6,
    },
  },
  media: {
    cdnUrl: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com',
    appsBaseUrl: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/apps',
    logosBaseUrl: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/logos',
    imagesBaseUrl: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/images',
  },
  okta: {
    clientId: import.meta.env.REACT_APP_OKTA_CLIENT_ID || '0oa26olz8zJVYCM7T5d7',
    issuer: import.meta.env.REACT_APP_OKTA_ISSUER || 'https://dev-login.g17.eco/oauth2/aus2a1qzwisQ8fZQX5d7',
  },
  sgx: {
    termsAndConditionsPdf: 'https://api2.sgx.com/sites/default/files/2018-09/SGXNET_TermsConditions.pdf',
    addendumTermsLink:
      'https://www.sgx.com/terms-use#ESG%20Portal%20Addendum%20to%20SGXNet%20User%20Terms%20&%20Conditions',
  },
  materialityTracker: {
    outdatedAssessmentDate: import.meta.env.REACT_APP_OUTDATED_ASSESSMENT_DATE || '2024-12-31T23:59:59.999Z',
  },
  crm: {
    formId: '00D3z000001BYGG',
  },
  minLogLevel: import.meta.env.REACT_APP_MIN_LOG_LEVEL || 'info',
  sentry: {
    dsn: import.meta.env.REACT_APP_SENTRY_DSN || 'https://<EMAIL>/1801084',
    tracesSampleRate: parseFloat(import.meta.env.REACT_APP_SENTRY_TRACE_SAMPLE_RATE || '0.1'),
    enabled: import.meta.env.REACT_APP_SENTRY_ENABLED || process.env.NODE_ENV === 'production',
    sendDefaultPii: import.meta.env.SENTRY_SEND_DEFAULT_PII ? process.env.SENTRY_SEND_DEFAULT_PII === 'true' : true,
  },
  faqsURL: 'https://wwghelp.zendesk.com/hc/en-us/sections/6691881229853-SDG-Contribution-FAQs',
  reportURL: 'https://wwghelp.zendesk.com/hc/en-us/requests/new?ticket_form_id=5300828213533',
  brochureURL: {
    assuranceTracker: 'https://www.g17.eco/assurance-tracker',
    companyTracker: 'https://www.g17.eco/company-tracker',
    // No longer have proper page, linking to book a demo page instead
    consultancy: 'https://www.g17.eco/book-a-demo',
    emissionsTracker: 'https://www.g17.eco/emissions-tracker',
    materialityTracker: 'https://www.g17.eco/materiality-tracker',
    portfolioTracker: 'https://www.g17.eco/portfolio-tracker',
    toli: 'https://www.g17.eco/toli-institute',
  },
  platform: {
    international: import.meta.env.REACT_APP_PLATFORM_INTERNATIONAL_URL || 'http://localhost:4001',
    singapore: import.meta.env.REACT_APP_PLATFORM_SINGAPORE_URL || 'http://sg.localhost:4001',
    uae: import.meta.env.REACT_APP_PLATFORM_UAE_URL || 'http://uae.localhost:4001',
    ksa: import.meta.env.REACT_APP_PLATFORM_KSA_URL || 'http://ksa.localhost:4001',
  },
  apiDocsUrl: import.meta.env.REACT_APP_API_DOCS_URL || 'https://docs.g17.eco',
  ledger: {
    enabled: Boolean(import.meta.env.REACT_APP_LEDGER_ENABLED),
  },
  release: import.meta.env.REACT_APP_RELEASE_NAME,
};
export default config;
