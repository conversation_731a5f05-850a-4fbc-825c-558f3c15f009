/*
 * Copyright (c) 2020-2024. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';
import { RouteErrorBoundary } from '@features/error';
import { AppRouteInterface } from '@g17eco/types/routes';
import { getFirstInitiativeGroupId } from '../../../selectors/routes';
import { AppLayout } from '../../../layout/AppLayout';
import Header from '@features/header';
import { Loader } from '@g17eco/atoms/loader';
import UTrModal from '@features/utr-modal';
import { generateUrl } from '@routes/util';
import { AppHeader } from '@features/header/AppHeader';
import Dashboard, { DashboardSection } from '@g17eco/molecules/dashboard';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { loadPortfolioTree } from '../../../slice/portfolioTreeSlice';
import PTLogo from '../../../images/apps/Portfolio_Tracker_logo.svg';
import { getCurrentPortfolioTree, isPortfolioTrackerExchange } from '../../../selectors/portfolio';
import { portfolioTrackerRouteMatcher } from '@routes/appRootMatcher';
import { useGetPortfolioByIdQuery } from '../api/portfolioTrackerApi';
import { skipToken } from '@reduxjs/toolkit/query';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { SiteAlertColors } from '@g17eco/slices/siteAlertsSlice';
import { portfolioTrackerRoutes } from './routes';
import { getHydratedRoutes } from '../../../constants/routeComponents';
import { portfolioTrackerInitiativeRoutes } from './portfolioTrackerRoutes';

const getPortfolioTrackerAppRoute = (isExchangeView = false): AppRouteInterface => ({
  appName: 'Portfolio Tracker',
  appIcon: PTLogo,
  routes: [
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_PORTFOLIO,
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_INSIGHTS,
    ...(isExchangeView
      ? [
          portfolioTrackerRoutes.PORTFOLIO_TRACKER_COMPANY_SETTINGS,
          portfolioTrackerRoutes.PORTFOLIO_TRACKER_BENCHMARKING,
        ]
      : []),
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_ADMIN_SETTINGS,
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_MANAGE_COMPANIES,
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_MANAGE_USERS,
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_CUSTOM_METRICS,
    portfolioTrackerRoutes.PORTFOLIO_TRACKER_INSIGHT_DASHBOARDS,
  ],
  hasReportingLevels: true,
});

const useDecideInitiativeId = (portfolioId: string) => {
  const firstInitiativeId = useAppSelector(getFirstInitiativeGroupId);
  return useMemo(() => {
    if (portfolioId !== '' || !firstInitiativeId) {
      return;
    }
    return generateUrl(portfolioTrackerRoutes.PORTFOLIO_TRACKER_PORTFOLIO, { portfolioId: firstInitiativeId });
  }, [firstInitiativeId, portfolioId]);
};
const routes = getHydratedRoutes(portfolioTrackerInitiativeRoutes);

/**
 * Portfolio Tracker App entry point
 */
export function PortfolioTrackerEntryRoute() {
  const portfolioIdpath = '/:_?/:_?/:portfolioId?';
  const history = useHistory();
  const dispatch = useAppDispatch();

  const match = useRouteMatch<{ portfolioId: string }>({ path: portfolioIdpath });
  const portfolioId = match?.params?.portfolioId ?? '';

  const initiativeTree = useAppSelector(getCurrentPortfolioTree);
  const portfolioTree = useAppSelector((state) => state.portfolioTree);
  const isExchangeView = useAppSelector(isPortfolioTrackerExchange);

  const { addSiteAlert } = useSiteAlert();
  const { isSuccess: loadedPortfolio, error } = useGetPortfolioByIdQuery(portfolioId || skipToken);


  // Redirect if initiativeId is available initiative tree, but missing in url
  const isRoutePath = useRouteMatch({ path: portfolioTrackerRoutes.PORTFOLIO_TRACKER.path, exact: true });
  const redirectUrl = useDecideInitiativeId(portfolioId);

  React.useEffect(() => {
    if (isRoutePath && redirectUrl) {
      history.push(redirectUrl);
    }
  }, [history, isRoutePath, redirectUrl]);

  React.useEffect(() => {
    dispatch(loadPortfolioTree());
  }, [dispatch]);

  React.useEffect(() => {
    if (error) {
      addSiteAlert({
        content: `Something wrong happens with your portfolio ${error.message ? `(${error.message})` : ''}.`,
        color: SiteAlertColors.Danger,
        timeout: 5000,
      });
      history.push(portfolioTrackerRoutes.PORTFOLIO_TRACKER.path);
    }
  }, [history, error, addSiteAlert]);

  if (!portfolioTree.loaded) {
    return <Loader />;
  }

  if (portfolioId && !loadedPortfolio) {
    return <Loader />;
  }

  // "/:root(portfolio-tracker)",
  return (
    <RouteErrorBoundary>
      <AppLayout
        Header={
          <Route path={portfolioIdpath}>
            <Header
              displayRootSwitcher
              routeMatcher={portfolioTrackerRouteMatcher}
              rootInitiatives={portfolioTree.data}
            >
              <AppHeader
                mapRoute={portfolioTrackerRoutes.PORTFOLIO_TRACKER_MAP}
                initiativeTree={initiativeTree}
                appRoute={getPortfolioTrackerAppRoute(isExchangeView)}
                route={portfolioTrackerRoutes.PORTFOLIO_TRACKER_PORTFOLIO}
              />
            </Header>
          </Route>
        }
      >
        <Switch>
          <Route {...portfolioTrackerRoutes.PORTFOLIO_TRACKER_MAP} />
          {/*"/:root(portfolio-tracker)/portfolio/:portfolioId?",*/}
          {Object.entries(routes).map(([, route]) => {
            return <Route key={`route_${route.id}`} {...route} exact={route.exact ?? false} />;
          })}
          <Route>
            {redirectUrl ? (
              <Loader />
            ) : (
              <Dashboard>
                <DashboardSection icon={'fa-folder-open'} title='Portfolio Tracker'>
                  <p>You currently don't have any Portfolio Trackers associated with this account.</p>
                  <p>Please contact our customer success team to set up your account.</p>
                </DashboardSection>
              </Dashboard>
            )}
          </Route>
        </Switch>
        <UTrModal />
      </AppLayout>
    </RouteErrorBoundary>
  );
}
