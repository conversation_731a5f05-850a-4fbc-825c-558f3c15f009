import { render, screen } from '@testing-library/react';
import { PPTXReportsMenu } from './PPTXReportsMenu';
import { PPTXReportScheme, PPTXTemplateScheme, PPTXColorScheme } from '@g17eco/types/download';
import { AI_MESSAGE } from '@constants/ai';
import { useAppSelector } from '@reducers/index';
import { JobStatus } from '@g17eco/types/background-jobs';
import { Mock } from 'vitest';

// Mock the Redux selector
vi.mock('@reducers/index', () => ({
  useAppSelector: vi.fn(),
}));

describe('PPTXReportsMenu', () => {
  const defaultScheme: PPTXReportScheme = {
    template: PPTXTemplateScheme.Default,
    color: PPTXColorScheme.Ocean,
  };

  const defaultProps = {
    generatingReport: undefined,
    generateReport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when user is staff', () => {
    beforeEach(() => {
      (useAppSelector as Mock).mockReturnValue(true); // isStaff = true
    });

    it('should render AI report generation button for staff users', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      expect(screen.getByText('Generate Report with AI')).toBeInTheDocument();

      // display privacy protection message about private metrics
      expect(screen.getByText('AI Report Generation:')).toBeInTheDocument();
      expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();

      // display the privacy alert
      const alert = screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC).closest('.alert');
      expect(alert).toBeInTheDocument();

      // display the info icon in the privacy alert
      const infoIcon = screen
        .getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)
        .closest('.alert')
        ?.querySelector('i.fal.fa-info-circle');
      expect(infoIcon).toBeInTheDocument();

      // Check that all buttons are present
      expect(screen.getByText('Generate Report')).toBeInTheDocument();
      expect(screen.getByText('Generate Report with AI')).toBeInTheDocument();
      expect(screen.getByText('DEBUG')).toBeInTheDocument();

      // And the privacy message is there
      expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();
    });
  });

  describe('when user is not staff', () => {
    beforeEach(() => {
      (useAppSelector as Mock).mockReturnValue(false); // isStaff = false
    });

    it('should not render AI report generation button for non-staff users', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      expect(screen.queryByText('Generate Report with AI')).not.toBeInTheDocument();

      // display privacy protection message for non-staff users
      expect(screen.getByText('AI Report Generation:')).toBeInTheDocument();
      expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();

      // render basic report generation button for non-staff users
      expect(screen.getByText('Generate Report')).toBeInTheDocument();
      expect(screen.getByText('DEBUG')).toBeInTheDocument();
    });
  });

  describe('report generation functionality', () => {
    beforeEach(() => {
      (useAppSelector as Mock).mockReturnValue(true); // isStaff = true
    });

    it('should call generateReport when Generate Report button is clicked', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      const generateButton = screen.getByText('Generate Report');
      generateButton.click();

      expect(defaultProps.generateReport).toHaveBeenCalledWith({
        scheme: defaultScheme,
      });
    });

    it('should call generateReport with AI flag when Generate Report with AI button is clicked', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      const generateAIButton = screen.getByText('Generate Report with AI');
      generateAIButton.click();

      expect(defaultProps.generateReport).toHaveBeenCalledWith({
        scheme: defaultScheme,
        useAISlideSuggestions: true,
      });
    });

    it('should call generateReport with debug flag when DEBUG button is clicked', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      const debugButton = screen.getByText('DEBUG');
      debugButton.click();

      expect(defaultProps.generateReport).toHaveBeenCalledWith({
        scheme: defaultScheme,
        debug: true,
      });
    });
  });

  describe('when report is generating', () => {
    const generatingReport = {
      _id: 'test-report-id',
      status: JobStatus.Processing,
      created: new Date().toISOString(),
      user: { _id: 'user-id', firstName: 'Test', surname: 'User' },
      initiativeId: 'initiative-id',
      taskId: 'task-id',
      surveyId: 'survey-id',
      templateScheme: PPTXTemplateScheme.Default,
    };

    beforeEach(() => {
      (useAppSelector as Mock).mockReturnValue(true); // isStaff = true
    });

    it('should show work in progress view when report is generating', () => {
      render(<PPTXReportsMenu {...defaultProps} generatingReport={generatingReport} />);

      expect(screen.getByText('AI Enhanced Sustainability Reports')).toBeInTheDocument();
      expect(screen.getByText(/Your report is being generated/)).toBeInTheDocument();
      expect(screen.queryByText('Generate Report')).not.toBeInTheDocument();
      expect(screen.queryByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).not.toBeInTheDocument();
    });
  });
});
