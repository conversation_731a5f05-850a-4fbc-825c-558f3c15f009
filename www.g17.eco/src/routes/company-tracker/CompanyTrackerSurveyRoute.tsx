/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { useRouteMatch } from 'react-router';
import { Switch, Route, useParams } from 'react-router-dom';
import SurveyContainer from '@features/survey-container/SurveyContainer';
import { SurveyConfiguration } from '@features/survey-configuration/SurveyConfiguration';
import SurveyAssurance from '@features/survey/assurance/SurveyAssurance';
import SurveyOverview from '../../features/survey/overview/overview';
import QuestionContainer from '../../features/survey/question/QuestionContainer';
import SurveyScope from '@features/survey-scope/SurveyScope';
import SurveyDelegation from '@apps/company-tracker/components/delegation/survey/survey-delegation/SurveyDelegation';
import SurveyImportExport from './survey-import-export';
import { sidebarPath } from '@features/survey-settings-toolbar/breadcrumbPath';
import { SurveyAddAssurance } from '../../types/survey';
import { ROUTES } from '../../constants/routes';

const SurveyComponent = ({ page }: { page?: string }) => {
  switch (page) {
    case 'configuration':
      return <SurveyConfiguration />
    case 'scope':
      return <SurveyScope />
    case 'delegation':
      return <SurveyDelegation />
    case 'import-export':
      return <SurveyImportExport />
    case 'overview':
    default:
      return <SurveyOverview />
  }
}

export const CompanyTrackerSurveyRoute = (props: { handleAddAssurance: (survey: SurveyAddAssurance) => void }) => {

  const { path } = useRouteMatch();
  const { surveyId, page } = useParams<{ surveyId: string, page?: string }>();

  return (
    <Switch>
      <Route path={`${path}/question/:questionId/:questionIndex/draft`}>
        <QuestionContainer isDraft={true} handleAddAssurance={props.handleAddAssurance} />
      </Route>

      <Route path={`${path}/question/:questionId/:questionIndex?`}>
        <QuestionContainer handleAddAssurance={props.handleAddAssurance} />
      </Route>
      <Route path={ROUTES.COMPANY_TRACKER_ASSURANCE.path}>
        <SurveyAssurance surveyId={surveyId} />
      </Route>

      <Route path={`${path}/${sidebarPath}`}>
        <SurveyContainer>
          <SurveyComponent page={page} />
        </SurveyContainer>
      </Route>
    </Switch>
  );
}
